// API 配置文件
export const API_CONFIG = {
  // 客户端配置
  CLIENT_ID: import.meta.env.VITE_CLIENT_ID || 'your_client_id',
  CLIENT_SECRET: import.meta.env.VITE_CLIENT_SECRET || 'your_client_secret',
  ENTERPRISE_CODE: import.meta.env.VITE_ENTERPRISE_CODE || 'your_enterprise_code',
  
  // API 基础配置
  BASE_URL: import.meta.env.VITE_API_URL || 'https://saas82.ukelink.net',
  TIMEOUT: 10000,
  
  // 默认参数
  DEFAULT_LANG: 'en-US',
  
  // 业务类型
  BUSINESS_TYPE: {
    GENERAL: '0',      // 通用业务
    REGISTER: '1',     // 注册
    FORGOT_PASSWORD: '2', // 找回密码
    LOGIN_REGISTER: '3'   // 短信验证码注册登录一体化
  },
  
  // 短信模板
  SMS_TEMPLATE: {
    VERIFICATION_CODE: 'sms_verification_code_template'
  }
}

// 获取客户端配置
export const getClientConfig = () => ({
  clientId: API_CONFIG.CLIENT_ID,
  clientSecret: API_CONFIG.CLIENT_SECRET,
  enterpriseCode: API_CONFIG.ENTERPRISE_CODE
})

// 获取短信配置
export const getSMSConfig = (businessType: string = API_CONFIG.BUSINESS_TYPE.GENERAL) => ({
  businessType,
  msgTemplateCode: API_CONFIG.SMS_TEMPLATE.VERIFICATION_CODE
})
