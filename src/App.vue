<template>
  <div id="app">
    <img alt="Vue logo" src="./assets/logo.png">
    <h1>Sentry 错误测试页面</h1>

    <div class="error-test-section">
      <h2>点击下面的按钮来测试不同类型的错误</h2>

      <div class="button-grid">
        <button @click="triggerJavaScriptError" class="error-btn js-error">
          JavaScript 运行时错误
        </button>

        <button @click="triggerPromiseRejection" class="error-btn promise-error">
          Promise 拒绝错误
        </button>

        <button @click="triggerCustomError" class="error-btn custom-error">
          自定义错误
        </button>

        <button @click="triggerNetworkError" class="error-btn network-error">
          网络请求错误
        </button>

        <button @click="triggerRenderError" class="error-btn render-error">
          组件渲染错误
        </button>

        <button @click="triggerTypeError" class="error-btn type-error">
          类型错误
        </button>
      </div>
    </div>

    <div v-if="showBrokenComponent" class="broken-component">
      <BrokenComponent />
    </div>

    <HelloWorld msg="Welcome to Your Vue.js App"/>
  </div>
</template>

<script>
import HelloWorld from './components/HelloWorld.vue'
import * as Sentry from "@sentry/vue"

export default {
  name: 'App',
  components: {
    HelloWorld
  },
  data() {
    return {
      showBrokenComponent: false
    }
  },
  methods: {
    // 1. JavaScript 运行时错误
    triggerJavaScriptError() {
      console.log('触发 JavaScript 运行时错误...')
      // 故意访问未定义的变量
      const result = undefinedVariable.someProperty
      console.log(result)
    },

    // 2. Promise 拒绝错误
    triggerPromiseRejection() {
      console.log('触发 Promise 拒绝错误...')
      new Promise((resolve, reject) => {
        setTimeout(() => {
          reject(new Error('这是一个 Promise 拒绝错误'))
        }, 1000)
      }).then(result => {
        console.log(result)
      })
      // 故意不添加 .catch() 来处理错误
    },

    // 3. 自定义错误
    triggerCustomError() {
      console.log('触发自定义错误...')
      try {
        throw new Error('这是一个自定义错误，用于测试 Sentry 错误捕获')
      } catch (error) {
        // 手动发送到 Sentry
        Sentry.captureException(error)
        console.error('自定义错误已发送到 Sentry:', error)
      }
    },

    // 4. 网络请求错误
    async triggerNetworkError() {
      console.log('触发网络请求错误...')
      try {
        const response = await fetch('https://nonexistent-api-endpoint-12345.com/data')
        const data = await response.json()
        console.log(data)
      } catch (error) {
        console.error('网络请求失败:', error)
        throw error // 让 Sentry 自动捕获
      }
    },

    // 5. 组件渲染错误
    triggerRenderError() {
      console.log('触发组件渲染错误...')
      this.showBrokenComponent = true
    },

    // 6. 类型错误
    triggerTypeError() {
      console.log('触发类型错误...')
      const nullValue = null
      // 故意在 null 上调用方法
      nullValue.toString()
    }
  }
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}
</style>
