<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuth } from '@/hooks/useAuth'
import { Icon } from '@/components/Icon'

const { t } = useI18n()
const router = useRouter()

// 使用共用的认证hooks
const {
  email,
  password,
  retypePassword,
  isLoading,
  showPassword,
  showRetypePassword,
  emailValid,
  passwordValid,
  passwordsMatch,
  passwordError,
  canContinueToCountry,
  checkUserExists,
} = useAuth()

// 处理继续到地区选择
const handleContinue = async () => {
  if (!canContinueToCountry.value) return

  try {
    const userExists = await checkUserExists(email.value)

    if (userExists) {
      ElMessage.error(t('register.user_exists'))
      setTimeout(() => {
        router.push('/login')
      }, 1500)
      return
    }

    // 跳转到地区选择页面
    router.push('/country-selection')
  } catch (error) {
    ElMessage.error(t('common.error'))
  }
}

const handleGoogleRegister = () => {
  console.log('Google register')
}

const handleAppleRegister = () => {
  console.log('Apple register')
}

const handleMobileRegister = () => {
  console.log('Mobile register')
}

const goToLogin = () => {
  router.push('/login')
}
</script>
<template>
  <div class="auth-container">
    <div class="auth-form">
      <h1 class="auth-title">{{ t('register.title') }}</h1>

      <!-- 邮箱输入 -->
      <div class="form-group">
        <label class="form-label">{{ t('login.email_label') }}</label>
        <el-input
          v-model="email"
          :placeholder="t('login.email_placeholder')"
          class="auth-input"
          size="large"
          type="email"
        />
      </div>

      <!-- 密码输入 -->
      <div class="form-group">
        <label class="form-label">{{ t('register.password_label') }}</label>
        <div class="password-input-wrapper">
          <el-input
            v-model="password"
            :type="showPassword ? 'text' : 'password'"
            :placeholder="t('register.password_placeholder')"
            class="auth-input password-input"
            size="large"
          />
          <el-icon class="password-toggle" @click="showPassword = !showPassword">
            <Icon v-if="showPassword" icon="svg-icon:show"></Icon>
            <Icon v-else icon="svg-icon:hide"></Icon>
          </el-icon>
        </div>
      </div>

      <!-- 重复密码输入 -->
      <div class="form-group">
        <label class="form-label">{{ t('register.confirm_password_label') }}</label>
        <div class="password-input-wrapper">
          <el-input
            v-model="retypePassword"
            :type="showRetypePassword ? 'text' : 'password'"
            :placeholder="t('register.confirm_password_placeholder')"
            class="auth-input password-input"
            size="large"
          />
          <el-icon class="password-toggle" @click="showRetypePassword = !showRetypePassword">
            <Icon v-if="showRetypePassword" icon="svg-icon:show"></Icon>
            <Icon v-else icon="svg-icon:hide"></Icon>
          </el-icon>
        </div>
        <div v-if="passwordError" class="error-message">* {{ passwordError }}</div>
      </div>

      <el-button
        type="primary"
        class="auth-primary-btn"
        size="large"
        :loading="isLoading"
        :disabled="!canContinueToCountry"
        @click="handleContinue"
      >
        {{ t('login.continue') }}
      </el-button>

      <!-- 密码要求提示 -->
      <div class="password-requirements">
        {{ t('register.password_error') }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
// 导入公共认证样式
@use '@web/styles/auth-common.scss';
</style>
