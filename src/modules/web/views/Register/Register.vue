<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuth } from '@/hooks/useAuth'
import { Icon } from '@/components/Icon'

const { t } = useI18n()
const router = useRouter()

// 使用共用的认证hooks
const {
  email,
  password,
  retypePassword,
  isLoading,
  showPassword,
  showRetypePassword,
  emailValid,
  passwordValid,
  passwordsMatch,
  passwordError,
  canContinueToCountry,
  checkUserExists,
  apiRegister,
  apiSendVerificationCode,
  showMessage,
  isAuthenticated,
} = useAuth()

// 新的注册表单状态
const userCode = ref('')
const registerPassword = ref('')
const confirmPassword = ref('')
const verificationCode = ref('')
const countryCode = ref('+1')
const showRegisterPassword = ref(false)
const showConfirmPassword = ref(false)
const codeSent = ref(false)
const countdown = ref(0)

// 检查是否已登录
if (isAuthenticated.value) {
  router.push('/')
}

// 真实注册表单验证
const canRegister = computed(() => {
  return (
    userCode.value.length > 0 &&
    registerPassword.value.length >= 6 &&
    confirmPassword.value === registerPassword.value &&
    verificationCode.value.length > 0
  )
})

// 发送验证码
const handleSendCode = async () => {
  if (!userCode.value) {
    showMessage('请输入手机号', 'error')
    return
  }

  try {
    await apiSendVerificationCode(userCode.value)
    codeSent.value = true
    countdown.value = 60

    // 开始倒计时
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
        codeSent.value = false
      }
    }, 1000)
  } catch (error) {
    console.error('Send code error:', error)
  }
}

// 处理真实 API 注册
const handleApiRegister = async () => {
  if (!canRegister.value) return

  try {
    const result = await apiRegister({
      userCode: userCode.value,
      password: registerPassword.value,
      countryCode: countryCode.value,
      msgCode: verificationCode.value,
    })

    if (result.success) {
      // 注册成功，跳转到登录页
      router.push('/login')
    }
  } catch (error) {
    console.error('Register error:', error)
  }
}

// 处理继续到地区选择
const handleContinue = async () => {
  if (!canContinueToCountry.value) return

  try {
    const userExists = await checkUserExists(email.value)

    if (userExists) {
      ElMessage.error(t('register.user_exists'))
      setTimeout(() => {
        router.push('/login')
      }, 1500)
      return
    }

    // 跳转到地区选择页面
    router.push('/country-selection')
  } catch (error) {
    ElMessage.error(t('common.error'))
  }
}

const handleGoogleRegister = () => {
  console.log('Google register')
}

const handleAppleRegister = () => {
  console.log('Apple register')
}

const handleMobileRegister = () => {
  console.log('Mobile register')
}

const goToLogin = () => {
  router.push('/login')
}
</script>
<template>
  <div class="auth-container">
    <div class="auth-form">
      <h1 class="auth-title">{{ t('register.title') }}</h1>

      <!-- 真实注册表单 -->
      <div class="real-register-form">
        <!-- 手机号输入 -->
        <div class="form-group">
          <label class="form-label">{{ t('register.phone_label') }}</label>
          <div class="phone-input-wrapper">
            <el-select v-model="countryCode" class="country-code-select" size="large">
              <el-option label="+1 (US)" value="+1" />
              <el-option label="+86 (CN)" value="+86" />
              <el-option label="+44 (UK)" value="+44" />
              <el-option label="+81 (JP)" value="+81" />
            </el-select>
            <el-input
              v-model="userCode"
              :placeholder="t('register.phone_placeholder')"
              class="auth-input phone-input"
              size="large"
              clearable
            />
          </div>
        </div>

        <!-- 验证码输入 -->
        <div class="form-group">
          <label class="form-label">{{ t('register.verification_code_label') }}</label>
          <div class="verification-input-wrapper">
            <el-input
              v-model="verificationCode"
              :placeholder="t('register.verification_code_placeholder')"
              class="auth-input verification-input"
              size="large"
              maxlength="6"
              clearable
            />
            <el-button
              type="primary"
              size="large"
              class="send-code-btn"
              :disabled="!userCode || codeSent"
              @click="handleSendCode"
            >
              {{ codeSent ? `${countdown}s` : t('register.send_code') }}
            </el-button>
          </div>
        </div>

        <!-- 密码输入 -->
        <div class="form-group">
          <label class="form-label">{{ t('register.password_label') }}</label>
          <div class="input-wrapper">
            <el-input
              v-model="registerPassword"
              :placeholder="t('register.password_placeholder')"
              class="auth-input"
              size="large"
              :type="showRegisterPassword ? 'text' : 'password'"
              clearable
            />
            <el-icon class="password-toggle" @click="showRegisterPassword = !showRegisterPassword">
              <Icon :icon="showRegisterPassword ? 'ep:hide' : 'ep:view'" />
            </el-icon>
          </div>
        </div>

        <!-- 确认密码输入 -->
        <div class="form-group">
          <label class="form-label">{{ t('register.confirm_password_label') }}</label>
          <div class="input-wrapper">
            <el-input
              v-model="confirmPassword"
              :placeholder="t('register.confirm_password_placeholder')"
              class="auth-input"
              size="large"
              :type="showConfirmPassword ? 'text' : 'password'"
              clearable
            />
            <el-icon class="password-toggle" @click="showConfirmPassword = !showConfirmPassword">
              <Icon :icon="showConfirmPassword ? 'ep:hide' : 'ep:view'" />
            </el-icon>
          </div>
        </div>

        <!-- 密码要求提示 -->
        <div class="password-requirements">
          {{ t('register.password_error') }}
        </div>

        <!-- 注册按钮 -->
        <el-button
          type="primary"
          size="large"
          class="auth-button"
          :loading="isLoading"
          :disabled="!canRegister"
          @click="handleApiRegister"
        >
          {{ t('register.register_button') }}
        </el-button>

        <!-- 登录链接 -->
        <div class="auth-footer">
          <span>{{ t('register.go_to_login') }}</span>
          <router-link to="/login" class="auth-link">
            {{ t('login.login_button') }}
          </router-link>
        </div>

        <!-- 社交注册 -->
        <div class="social-login">
          <div class="divider">
            <span>{{ t('login.or') }}</span>
          </div>

          <div class="social-buttons">
            <el-button class="social-button google" @click="handleGoogleRegister">
              <Icon icon="logos:google-icon" />
              {{ t('login.google') }}
            </el-button>

            <el-button class="social-button apple" @click="handleAppleRegister">
              <Icon icon="ic:baseline-apple" />
              {{ t('login.apple') }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 原有的演示注册表单（隐藏） -->
      <div class="demo-register-form" style="display: none">
        <!-- 邮箱输入 -->
        <div class="form-group">
          <label class="form-label">{{ t('login.email_label') }}</label>
          <el-input
            v-model="email"
            :placeholder="t('login.email_placeholder')"
            class="auth-input"
            size="large"
            type="email"
          />
        </div>

        <!-- 密码输入 -->
        <div class="form-group">
          <label class="form-label">{{ t('register.password_label') }}</label>
          <div class="password-input-wrapper">
            <el-input
              v-model="password"
              :type="showPassword ? 'text' : 'password'"
              :placeholder="t('register.password_placeholder')"
              class="auth-input password-input"
              size="large"
            />
            <el-icon class="password-toggle" @click="showPassword = !showPassword">
              <Icon v-if="showPassword" icon="svg-icon:show"></Icon>
              <Icon v-else icon="svg-icon:hide"></Icon>
            </el-icon>
          </div>
        </div>

        <!-- 重复密码输入 -->
        <div class="form-group">
          <label class="form-label">{{ t('register.confirm_password_label') }}</label>
          <div class="password-input-wrapper">
            <el-input
              v-model="retypePassword"
              :type="showRetypePassword ? 'text' : 'password'"
              :placeholder="t('register.confirm_password_placeholder')"
              class="auth-input password-input"
              size="large"
            />
            <el-icon class="password-toggle" @click="showRetypePassword = !showRetypePassword">
              <Icon v-if="showRetypePassword" icon="svg-icon:show"></Icon>
              <Icon v-else icon="svg-icon:hide"></Icon>
            </el-icon>
          </div>
          <div v-if="passwordError" class="error-message">* {{ passwordError }}</div>
        </div>

        <el-button
          type="primary"
          class="auth-primary-btn"
          size="large"
          :loading="isLoading"
          :disabled="!canContinueToCountry"
          @click="handleContinue"
        >
          {{ t('login.continue') }}
        </el-button>

        <!-- 密码要求提示 -->
        <div class="password-requirements">
          {{ t('register.password_error') }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
// 导入公共认证样式
@use '@web/styles/auth-common.scss';
</style>
