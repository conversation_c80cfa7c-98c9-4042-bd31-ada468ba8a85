<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuth } from '@/hooks/useAuth'
import { Icon } from '@/components/Icon'
import { ref, computed } from 'vue'
import { countries, getCountryName } from '@/data/countries'

const { t } = useI18n()
const router = useRouter()

// 使用共用的认证hooks
const {
  email,
  password,
  retypePassword,
  isLoading,
  showPassword,
  showRetypePassword,
  emailValid,
  passwordValid,
  passwordsMatch,
  passwordError,
  canContinueToCountry,
  canSubmitRegister,
  checkUserExists,
  register,
  selectedCountry,
  registerStep,
  continueToCountrySelection,
  backToRegisterForm,
  setSelectedCountry,
  isAuthenticated,
} = useAuth()

// 新的注册表单状态
const userCode = ref('')
const registerPassword = ref('')
const confirmPassword = ref('')
const verificationCode = ref('')
const countryCode = ref('+1')
const showRegisterPassword = ref(false)
const showConfirmPassword = ref(false)
const codeSent = ref(false)
const countdown = ref(0)

// 检查是否已登录
if (isAuthenticated.value) {
  router.push('/')
}

// 真实注册表单验证
const canRegister = computed(() => {
  return (
    userCode.value.length > 0 &&
    registerPassword.value.length >= 6 &&
    confirmPassword.value === registerPassword.value &&
    verificationCode.value.length > 0
  )
})

// 发送验证码
const handleSendCode = async () => {
  if (!userCode.value) {
    showMessage('请输入手机号', 'error')
    return
  }

  try {
    await apiSendVerificationCode(userCode.value)
    codeSent.value = true
    countdown.value = 60

    // 开始倒计时
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
        codeSent.value = false
      }
    }, 1000)
  } catch (error) {
    console.error('Send code error:', error)
  }
}

// 处理真实 API 注册
const handleApiRegister = async () => {
  if (!canRegister.value) return

  try {
    const result = await apiRegister({
      userCode: userCode.value,
      password: registerPassword.value,
      countryCode: countryCode.value,
      msgCode: verificationCode.value,
    })

    if (result.success) {
      // 注册成功，跳转到登录页
      router.push('/login')
    }
  } catch (error) {
    console.error('Register error:', error)
  }
}

// 处理继续到地区选择
const handleContinue = async () => {
  if (!canContinueToCountry.value) return

  try {
    const userExists = await checkUserExists(email.value)

    if (userExists) {
      ElMessage.error(t('register.user_exists'))
      setTimeout(() => {
        router.push('/login')
      }, 1500)
      return
    }

    // 切换到国家选择步骤
    continueToCountrySelection()
  } catch (error) {
    ElMessage.error(t('common.error'))
  }
}

// 处理注册
const handleRegister = async () => {
  if (!canSubmitRegister.value) return

  const result = await register(email.value, password.value, selectedCountry.value)

  if (result.success) {
    ElMessage.success(result.message || t('register.register_success'))
    // 注册成功后跳转到登录页面
    setTimeout(() => {
      router.push('/login')
    }, 1500)
  } else {
    ElMessage.error(result.message || t('register.register_failed'))
    if (result.shouldRedirectToLogin) {
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    }
  }
}

const handleGoogleRegister = () => {
  console.log('Google register')
}

const handleAppleRegister = () => {
  console.log('Apple register')
}

const handleMobileRegister = () => {
  console.log('Mobile register')
}

const goToLogin = () => {
  router.push('/login')
}
</script>
<template>
  <div class="auth-container">
    <div class="auth-form">
      <!-- 注册表单步骤 -->
      <div v-if="registerStep === 'form'" class="register-form-step">
        <h1 class="auth-title">{{ t('register.title') }}</h1>
        <!-- 邮箱输入 -->
        <div class="form-group">
          <label class="form-label">{{ t('login.email_label') }}</label>
          <el-input
            v-model="email"
            :placeholder="t('login.email_placeholder')"
            class="auth-input"
            size="large"
            type="email"
          />
        </div>

        <!-- 密码输入 -->
        <div class="form-group">
          <label class="form-label">{{ t('register.password_label') }}</label>
          <div class="password-input-wrapper">
            <el-input
              v-model="password"
              :type="showPassword ? 'text' : 'password'"
              :placeholder="t('register.password_placeholder')"
              class="auth-input password-input"
              size="large"
            />
            <el-icon class="password-toggle" @click="showPassword = !showPassword">
              <Icon v-if="showPassword" icon="svg-icon:show"></Icon>
              <Icon v-else icon="svg-icon:hide"></Icon>
            </el-icon>
          </div>
        </div>

        <!-- 重复密码输入 -->
        <div class="form-group">
          <label class="form-label">{{ t('register.confirm_password_label') }}</label>
          <div class="password-input-wrapper">
            <el-input
              v-model="retypePassword"
              :type="showRetypePassword ? 'text' : 'password'"
              :placeholder="t('register.confirm_password_placeholder')"
              class="auth-input password-input"
              size="large"
            />
            <el-icon class="password-toggle" @click="showRetypePassword = !showRetypePassword">
              <Icon v-if="showRetypePassword" icon="svg-icon:show"></Icon>
              <Icon v-else icon="svg-icon:hide"></Icon>
            </el-icon>
          </div>
          <div v-if="passwordError" class="error-message">* {{ passwordError }}</div>
        </div>

        <el-button
          type="primary"
          class="auth-primary-btn"
          size="large"
          :loading="isLoading"
          :disabled="!canContinueToCountry"
          @click="handleContinue"
        >
          {{ t('login.continue') }}
        </el-button>

        <!-- 密码要求提示 -->
        <div class="password-requirements">
          {{ t('register.password_error') }}
        </div>
      </div>

      <!-- 国家选择步骤 -->
      <div v-else-if="registerStep === 'country'" class="country-selection-step">
        <div class="country-selection-header">
          <button class="back-button" @click="backToRegisterForm()">
            <Icon icon="ep:arrow-left" />
            {{ t('common.back') }}
          </button>
          <h1 class="auth-title">{{ t('register.select_residence') }}</h1>
        </div>

        <!-- 国家选择 -->
        <div class="form-group">
          <label class="form-label">{{ t('register.country_region') }}</label>
          <el-select
            v-model="selectedCountry"
            :placeholder="t('register.select_country_placeholder')"
            class="auth-input country-select"
            size="large"
            filterable
            @change="setSelectedCountry"
          >
            <el-option
              v-for="country in countries"
              :key="country.code"
              :label="getCountryName(country, t)"
              :value="country.code"
            >
              <div class="country-option">
                <span class="country-flag">{{ country.flag }}</span>
                <span class="country-name">{{ getCountryName(country, t) }}</span>
              </div>
            </el-option>
          </el-select>
        </div>

        <!-- 注册按钮 -->
        <el-button
          type="primary"
          class="auth-primary-btn"
          size="large"
          :loading="isLoading"
          :disabled="!canSubmitRegister"
          @click="handleRegister"
        >
          {{ t('register.sign_up') }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
// 导入公共认证样式
@use '@web/styles/auth-common.scss';

// 国家选择步骤样式
.country-selection-step {
  .country-selection-header {
    margin-bottom: 32px;

    .back-button {
      display: flex;
      align-items: center;
      gap: 8px;
      background: none;
      border: none;
      color: var(--primary-color);
      font-size: 16px;
      cursor: pointer;
      margin-bottom: 16px;
      padding: 8px 0;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .country-select {
    width: 100%;
  }

  .country-option {
    display: flex;
    align-items: center;
    gap: 12px;

    .country-flag {
      font-size: 20px;
    }

    .country-name {
      flex: 1;
    }
  }
}
</style>
