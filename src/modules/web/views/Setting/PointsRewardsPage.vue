<script setup lang="ts">
import SettingSidebar from './components/SettingSidebar.vue'
import { Pagination } from '@/components/Pagination/index'
import Copyright from '@web/components/Copyright.vue'
import Footer from '@web/components/Footer.vue'
import DownloadApp from '@web/views/Home/DownloadApp.vue'
import EsimEmpty from '@web/components/EsimEmpty.vue'
import { useI18n } from 'vue-i18n'
import { computed, onMounted, ref } from 'vue'
import type { PointsTransaction } from '@/api/Points/types.ts'
import { Icon } from '@/components/Icon'
const { t } = useI18n()

// 用户积分信息
const pointsInfo = ref({
  totalPoints: 12030,
  availablePoints: 12030,
  pointsBalance: 120.0,
  currencyType: 'USD',
  exchangeRate: 100, // 100积分 = 1美元
})

// 模拟积分历史数据
const pointsHistory = ref<PointsTransaction[]>([
  {
    id: 1,
    type: 'earn',
    amount: 10,
    description: 'Purchase rewards',
    date: '2025/01/01',
    status: 'completed',
  },
  {
    id: 2,
    type: 'earn',
    amount: 10,
    description: 'Purchase rewards',
    date: '2025/01/02',
    status: 'completed',
  },
  {
    id: 3,
    type: 'earn',
    amount: 10,
    description: 'Purchase rewards',
    date: '2025/01/03',
    status: 'completed',
  },
  {
    id: 4,
    type: 'earn',
    amount: 10,
    description: 'Purchase rewards',
    date: '2025/01/04',
    status: 'completed',
  },
  {
    id: 5,
    type: 'earn',
    amount: 10,
    description: 'Purchase rewards',
    date: '2025/01/05',
    status: 'completed',
  },
])

// 分页相关
const pagination = ref({
  currentPage: 1,
  perPageCount: 5,
  totalCount: 12,
  totalPageCount: 3,
})

// 计算当前页显示的历史记录
const currentPageHistory = computed(() => {
  const start = (pagination.value.currentPage - 1) * pagination.value.perPageCount
  const end = start + pagination.value.perPageCount
  return pointsHistory.value.slice(start, end)
})

// 格式化日期
const formatDate = (date: string | number) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

// 获取交易类型图标
const getTransactionIcon = (type: string) => {
  switch (type) {
    case 'EARN':
      return 'svg-icon:points'
    case 'REDEEM':
      return 'svg-icon:order'
    default:
      return 'svg-icon:points'
  }
}

// 获取交易类型颜色
const getTransactionColor = (type: string) => {
  switch (type) {
    case 'EARN':
      return '#00C851'
    case 'REDEEM':
      return '#FF6B6B'
    default:
      return '#6B7280'
  }
}

// 下载应用
const handleDownloadApp = () => {
  console.log('Download GlocalMe App')
  // 这里应该跳转到应用下载页面
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.value.currentPage = page
  console.log('Page changed to:', page)
}

onMounted(() => {
  console.log('Points & Rewards page mounted')
})
</script>

<template>
  <div class="account-settings-page">
    <div class="container-box">
      <div class="settings-layout">
        <!-- 左侧边栏 -->
        <SettingSidebar />

        <!-- 右侧内容区域 -->
        <div class="content-area">
          <div class="settings-content">
            <div class="points-rewards-page">
              <div class="points-header">
                <h2 class="points-title">{{ t('pointsRewards.title') }}</h2>
              </div>

              <div class="container">
                <!-- 积分余额卡片 -->
                <div class="points-balance-card">
                  <div class="balance-item">
                    <div class="balance-value">{{ pointsInfo.totalPoints.toLocaleString() }}</div>
                    <div class="balance-label">
                      <Icon icon="svg-icon:star" :size="16" />
                      {{ t('pointsRewards.points') }}
                    </div>
                  </div>

                  <div class="balance-item">
                    <div class="balance-value">${{ pointsInfo.pointsBalance.toFixed(2) }}</div>
                    <div class="balance-label">
                      <Icon icon="svg-icon:money" :size="16" />
                      {{ t('pointsRewards.balance') }}
                    </div>
                  </div>
                </div>

                <!-- 兑换产品提示 -->
                <div class="redeem-info-card">
                  <div class="redeem-info-content">
                    <div class="redeem-text">
                      <h3>
                        <Icon icon="svg-icon:promo" :size="24" />
                        {{ t('pointsRewards.redeemTitle') }}
                      </h3>
                      <p>{{ t('pointsRewards.redeemDescription') }}</p>
                    </div>
                  </div>

                  <el-button class="download-app-btn" @click="handleDownloadApp">
                    {{ t('pointsRewards.downloadApp') }}
                  </el-button>
                </div>
              </div>
              <!-- 购买历史 -->
              <div class="purchase-history">
                <h3 class="history-title">{{ t('pointsRewards.purchaseHistory') }}</h3>

                <div
                  class="history-list"
                  v-if="currentPageHistory && currentPageHistory.length > 0"
                >
                  <div
                    v-for="transaction in currentPageHistory"
                    :key="transaction.id"
                    class="history-item"
                  >
                    <div class="history-content">
                      <div class="history-info">
                        <div class="history-type">{{ t('pointsRewards.purchaseRewards') }}</div>
                        <div class="history-date">
                          {{ formatDate(transaction.date) }}
                        </div>
                      </div>
                      <div class="history-points">
                        <span class="points-amount">
                          +{{ transaction.amount }} {{ t('pointsRewards.pointsEarned') }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else>
                  <EsimEmpty />
                </div>
                <!-- 分页器 -->
                <Pagination
                  :current-page="pagination.currentPage"
                  :per-page-count="pagination.perPageCount"
                  :total-count="pagination.totalCount"
                  :total-page-count="pagination.totalPageCount"
                  translation-prefix="pointsRewards.pagination"
                  @page-change="handlePageChange"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <DownloadApp />
  <Footer />
  <Copyright />
</template>

<style scoped lang="scss">
.account-settings-page {
  padding: 72px 0 253px;
}

.settings-layout {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

// 右侧内容区域样式
.content-area {
  flex: 1;
  min-width: 0;
  width: 100%;
}

// 响应式设计
@media (max-width: 1024px) {
  .settings-layout {
    flex-direction: column;
    gap: 24px;
  }
}
</style>
<style scoped lang="scss">
.points-rewards-page {
  padding: 0;
}

.points-header {
  margin-bottom: 32px;
}

.points-title {
  font-size: 32px;
  font-weight: 600;
  color: #000000;
  margin: 0;
}

.container {
  border: 1px solid #e0e0e0;
  background: white;
  border-radius: 20px;
  padding: 59px 24px 24px 24px;
}

.points-balance-card {
  display: flex;
  gap: 60px;
  margin-bottom: 32px;
}

.balance-item {
  text-align: center;
  width: 50%;

  &:first-child {
    border-right: 1px solid #e0e0e0;
    padding: 9px 0;
  }
}

.balance-value {
  font-size: 36px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 16px;
  line-height: 1;
}

.balance-label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 20px;
  margin-bottom: 16px;
  color: var(--primary-color);
  font-weight: 500;
}

.balance-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00c851;
}

.redeem-info-card {
  padding: 24px;
}

.redeem-info-content {
  margin-bottom: 20px;
}

.redeem-text {
  flex: 1;

  h3 {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0 0 12px 0;

    i {
      vertical-align: bottom;
      margin-right: 14px;
    }
  }

  p {
    font-size: 20px;
    color: rgba(0, 0, 0, 0.5);
    line-height: 28px;
    margin-bottom: 0;
  }
}

.download-app-btn {
  width: 280px;
  height: 56px;
  background: white;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 20px;
  font-size: 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
  }

  &:active {
    transform: translateY(1px);
  }
}

.purchase-history {
  margin-bottom: 40px;
  margin-top: 100px;
}

.history-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 24px 0;
}

.history-list {
  margin-bottom: 24px;
}

.history-item {
  box-shadow: inset 0px -1px 0px 0px #e0e0e0;
  border: 1px solid #e0e0e0;
  background: white;
  height: 100px;
  padding: 24px;
  border-radius: 20px;
  margin-bottom: 24px;

  &:last-child {
    border-bottom: none;
  }
}

.history-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-info {
  flex: 1;
}

.history-type {
  font-size: 20px;
  font-weight: 500;
  color: var(--primary-color);
  margin-bottom: 12px;
}

.history-date {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.5);
}

.history-points {
  text-align: right;
  margin-top: -10px;
  color: var(--primary-color);
}

.points-amount {
  font-size: 20px;
  font-weight: 600;
}
</style>
