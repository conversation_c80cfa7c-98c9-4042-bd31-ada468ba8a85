<script lang="ts" setup>
import EsimSlider from '@/components/EsimSlider/index.vue'
import EsimStages from '@/components/EsimStages/index.vue'
import EsimDestination from '@web/components/EsimDestination.vue'
import EsimEmpty from '@web/components/EsimEmpty.vue'
import { useI18n } from 'vue-i18n'
import { destinationTypes } from '@/data/destinations'
import { useDestinations, useDestinationNavigation } from '@/hooks/useDestinations'
import { computed } from 'vue'

const { t } = useI18n()

// 使用目的地数据 hooks
const { loading, filteredDestinations, setActiveTab, setLetterFilter } = useDestinations()

// 使用目的地导航 hooks
const { goToPlanDetail } = useDestinationNavigation()

// eSIMS选择
const list = computed(() => {
  return destinationTypes.map((item) => {
    item.label = t(item.label)
    return item
  })
})
// 处理目的地点击
const handleTo = (item: any) => {
  goToPlanDetail(item)
}

// 处理标签切换
const handleTabChange = (item) => {
  setActiveTab(item.value)
}

// 处理过滤器变化
const handleFilter = function (item: any) {
  // 根据选中的字母范围过滤数据
  // item.value 包含字母范围，如 'ABCDEFG' 或空字符串（表示全部）
  console.log('选中的字母范围:', item.label, item.value)
  setLetterFilter(item.value || '')
}
</script>

<template>
  <div class="shop-plans">
    <EsimSlider style="width: 646px" :list="list" @change="handleTabChange"> </EsimSlider>

    <div style="margin: 56px 0">
      <EsimStages @change="handleFilter"></EsimStages>
    </div>

    <div class="container-box" v-loading="loading">
      <!-- 目的地列表 -->
      <EsimDestination :list="filteredDestinations" @click-cell="handleTo"> </EsimDestination>

      <!-- 无数据提示 -->
      <template v-if="!loading && filteredDestinations.length === 0">
        <EsimEmpty></EsimEmpty>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.shop-plans {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 160px;
}
</style>
