.plans-section {
  margin-bottom: 80px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #000;
}

.cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.plan-card {
  width: calc(25% - 15px);
  background-color: #fff;
  border-radius: 12px;
  padding: 24px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  cursor: pointer;
  border: 2px solid #ffffff;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  @media (max-width: 1200px) {
    width: calc(33% - 15px);
  }

  @media (max-width: 768px) {
    width: calc(50% - 15px);
  }

  @media (max-width: 480px) {
    width: 100%;
  }

  &.selected {
    border-color: var(--base-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  }
}

.plan-data {
  font-size: 40px;
  line-height: 1;
  font-weight: 700;
  margin-top: 20px;
  margin-bottom: 24px;
}

.hot-onsale {
  display: flex;
  align-items: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 24px;

  .hot-network {
    padding: 4px 6px;
    background-color: #222222;
    border-radius: 4px;
  }

  .hot-text {
    margin-left: 8px;
    padding: 4px 6px;
    background-color: #ff9000;
    border-radius: 4px;
  }
}

.plan-inner {
  flex: 1;
  min-height: 0;
  text-align: center;
}

.plan-days {
  font-size: 20px;
  line-height: 1;
  color: #000000;
  margin-bottom: 44px;
}

.coverage-info {
  width: 100%;
  margin-bottom: 20px;
  text-align: center;

  .coverage-label {
    color: var(--vt-c-text-light-2);
    font-size: 20px;
    margin-bottom: 5px;
  }

  .coverage-region {
    font-size: 20px;
  }

  .region-link {
    color: var(--base-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      text-decoration: underline;
    }
  }

  .arrow-icon {
    margin-left: 4px;
    font-size: 16px;
  }
}

.plan-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 38px;

  .currency {
    font-size: 20px;
    color: #000000;
    margin-right: 5px;
  }

  .price {
    font-size: 32px;
    font-weight: 700;
    color: #000000;
  }
  .original-price {
    font-size: 20px;
    color: #999;
    text-decoration: line-through;
    margin-left: 8px;
  }
}

.plan-indicator {
  margin-top: auto;

  .circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;

    &.checked {
      border-color: var(--base-color);
      background-color: var(--base-color);
    }
  }
}
