<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuth } from '@/hooks/useAuth'
import { countries, getCountryName } from '@/data/countries'
import { ElMessage } from 'element-plus'

const { t, locale } = useI18n()
const router = useRouter()

const {
  email,
  password,
  selectedCountry,
  isLoading,
  canSubmitRegister,
  register,
  setSelectedCountry,
  resetForm,
} = useAuth()

// 下拉框选项
const countryOptions = computed(() => {
  return countries.map((country) => ({
    value: country.code,
    label: getCountryName(country, locale.value),
    flag: country.flag,
  }))
})

// 处理国家选择
const handleCountryChange = (countryCode: string) => {
  setSelectedCountry(countryCode)
}

// 处理注册
const handleRegister = async () => {
  if (!canSubmitRegister.value) return

  const result = await register(email.value, password.value, selectedCountry.value)

  if (result.success) {
    ElMessage.success(result.message || t('register.register_success'))
    resetForm()
    router.push('/')
  } else {
    ElMessage.error(result.message || t('register.register_failed'))
    if (result.shouldRedirectToLogin) {
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    }
  }
}
</script>

<template>
  <div class="auth-container">
    <div class="auth-form country-selection-form">
      <!-- 头部 -->
      <div class="country-selection-header">
        <h1 class="auth-title">{{ t('register.select_residence') }}</h1>
      </div>

      <!-- 国家选择下拉框 -->
      <div class="form-group">
        <label class="form-label">{{ t('register.country_region') }}</label>
        <el-select
          v-model="selectedCountry"
          :placeholder="t('register.select_country_placeholder')"
          class="country-select"
          size="large"
          filterable
          clearable
          @change="handleCountryChange"
        >
          <el-option
            v-for="option in countryOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          >
            <div class="country-option">
              <span class="country-flag">{{ option.flag }}</span>
              <span class="country-name">{{ option.label }}</span>
            </div>
          </el-option>
        </el-select>
      </div>

      <!-- 注册按钮 -->
      <el-button
        type="primary"
        class="auth-primary-btn"
        size="large"
        :loading="isLoading"
        :disabled="!canSubmitRegister"
        @click="handleRegister"
      >
        {{ t('register.sign_up') }}
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
// 导入公共样式
@use '@web/styles/auth-common.scss';

.country-selection-header {
  margin-bottom: 32px;

  .back-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: var(--base-color);
    font-size: 14px;
    cursor: pointer;
    margin-bottom: 16px;
    padding: 0;

    &:hover {
      text-decoration: underline;
    }
  }
}

// 国家选择下拉框样式
.country-select {
  width: 100%;

  :deep(.el-select__wrapper) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    padding: 12px 16px;
    font-size: 20px;
    line-height: 72px;
    min-height: 72px;
    &:hover {
      border-color: #9ca3af;
    }

    &.is-focused {
      box-shadow: var(--base-shadow);
      border: 1px solid var(--base-color);
    }
  }

  :deep(.el-select__placeholder) {
    color: #cccccc !important;
    font-size: 20px;
  }

  :deep(.el-select__selected-item) {
    font-size: 20px;
    color: var(--primary-color);
  }
}

// 下拉选项样式
.country-option {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;

  .country-flag {
    width: 20px;
    text-align: center;
    flex-shrink: 0;
  }

  .country-name {
    flex: 1;
  }
}

// 表单组样式调整
.form-group {
  margin-bottom: 32px;
}
</style>
