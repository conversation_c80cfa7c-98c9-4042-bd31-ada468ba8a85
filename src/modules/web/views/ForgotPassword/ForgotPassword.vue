<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
// import { EyeOff, Eye } from '@element-plus/icons-vue'
import { useAuth } from '@/hooks/useAuth'
import { Icon } from '@/components/Icon'

const { t } = useI18n()
const router = useRouter()
// 使用共用的认证hooks
const {
  email,
  verificationCode,
  password,
  retypePassword,
  isLoading,
  showPassword,
  showRetypePassword,
  codeSent,
  countdown,
  emailValid,
  passwordValid,
  passwordsMatch,
  codeValid,
  passwordError,
  sendVerificationCode,
  resetPassword,
  resetForm,
} = useAuth()

// 计算属性
const canSendCode = computed(() => {
  return emailValid.value && countdown.value === 0
})

const canSave = computed(() => {
  return emailValid.value && codeValid.value && passwordValid.value && passwordsMatch.value
})

// 发送验证码
const handleSendCode = async () => {
  if (!canSendCode.value) return

  const result = await sendVerificationCode(email.value)

  if (result.success) {
    ElMessage.success(result.message)
  } else {
    ElMessage.error(result.message)
  }
}

// 保存新密码
const handleSave = async () => {
  if (!canSave.value) return

  const result = await resetPassword(email.value, verificationCode.value, password.value)

  if (result.success) {
    ElMessage.success(result.message)
    resetForm()
    router.push('/login')
  } else {
    ElMessage.error(result.message)
  }
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<template>
  <div class="auth-container">
    <div class="auth-form forgot-password-form">
      <h1 class="auth-title">
        Next, we'll send a verification code to your email. Please enter the 6-digit code we sent.
      </h1>

      <!-- 邮箱输入 -->
      <div class="form-group">
        <label class="form-label">Email</label>
        <el-input
          v-model="email"
          placeholder="<EMAIL>"
          class="auth-input"
          size="large"
          type="email"
          :readonly="codeSent"
        />
      </div>
      <!-- 验证码输入 -->
      <div class="form-group">
        <label class="form-label">Verification Code</label>
        <div class="verification-wrapper">
          <el-input
            v-model="verificationCode"
            placeholder="Verification code"
            class="auth-input verification-input"
            size="large"
            maxlength="6"
          />
          <el-button
            class="send-code-btn"
            :class="{ disabled: !canSendCode }"
            :disabled="!canSendCode"
            :loading="isLoading && !codeSent"
            @click="handleSendCode"
          >
            {{ countdown > 0 ? `Resend code(${countdown})` : 'Send code' }}
          </el-button>
        </div>
      </div>

      <!-- 密码输入 -->
      <div class="form-group">
        <label class="form-label">Password</label>
        <div class="password-input-wrapper">
          <el-input
            v-model="password"
            :type="showPassword ? 'text' : 'password'"
            placeholder="Password"
            class="auth-input password-input"
            size="large"
          />
          <el-icon class="password-toggle" @click="showPassword = !showPassword">
            <Icon v-if="showPassword" icon="svg-icon:show"></Icon>
            <Icon v-else icon="svg-icon:hide"></Icon>
          </el-icon>
        </div>
      </div>

      <!-- 重复密码输入 -->
      <div class="form-group">
        <label class="form-label">Retype Password</label>
        <div class="password-input-wrapper">
          <el-input
            v-model="retypePassword"
            :type="showRetypePassword ? 'text' : 'password'"
            placeholder="Password"
            class="auth-input password-input"
            size="large"
          />
          <el-icon class="password-toggle" @click="showRetypePassword = !showRetypePassword">
            <Icon v-if="showRetypePassword" icon="svg-icon:show"></Icon>
            <Icon v-else icon="svg-icon:hide"></Icon>
          </el-icon>
        </div>
        <div v-if="passwordError" class="error-message">* {{ passwordError }}</div>
      </div>

      <el-button
        type="primary"
        class="auth-secondary-btn"
        :class="{ disabled: !canSave }"
        size="large"
        :loading="isLoading && codeSent"
        :disabled="!canSave"
        @click="handleSave"
      >
        Save
      </el-button>

      <!-- 密码要求提示 -->
      <div class="password-requirements">
        Password must be 6-20 characters long and include at least two of the following: letters,
        numbers, or symbols. It also cannot be one you've used before.
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
// 导入公共认证样式
@use '@web/styles/auth-common.scss';
</style>
