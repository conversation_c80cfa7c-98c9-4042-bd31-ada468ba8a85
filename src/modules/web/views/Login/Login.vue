<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { Icon } from '@/components/Icon'
import { useAuth } from '@/hooks/useAuth'
import { ElMessage } from 'element-plus'

const { t } = useI18n()
const router = useRouter()

// 使用共用的认证hooks
const {
  email,
  password,
  isLoading,
  showPassword,
  emailValid,
  checkUserExists,
  login,
  apiLogin,
  showMessage,
  isAuthenticated,
} = useAuth()

// 表单状态
const currentStep = ref<'email' | 'password'>('email')
const userCode = ref('')
const loginPassword = ref('')
const showLoginPassword = ref(false)

// 检查是否已登录
if (isAuthenticated.value) {
  router.push('/')
}

const canContinue = computed(() => {
  if (currentStep.value === 'email') {
    return emailValid.value
  } else if (currentStep.value === 'password') {
    return password.value.length > 0
  }
  return false
})

// 真实登录表单验证
const canLogin = computed(() => {
  return userCode.value.length > 0 && loginPassword.value.length > 0
})

// 处理真实 API 登录
const handleApiLogin = async () => {
  if (!canLogin.value) return

  try {
    const result = await apiLogin({
      userCode: userCode.value,
      password: loginPassword.value,
    })

    if (result.success) {
      // 登录成功，跳转到首页
      router.push('/')
    }
  } catch (error) {
    console.error('Login error:', error)
  }
}

const handleContinue = async () => {
  if (!canContinue.value) return

  if (currentStep.value === 'email') {
    // 检查邮箱是否已注册
    const userExists = await checkUserExists(email.value)
    if (userExists) {
      // 已注册，显示登录密码输入
      currentStep.value = 'password'
    } else {
      // 未注册，跳转到注册页面
      router.push('/register')
    }
  } else if (currentStep.value === 'password') {
    // 使用hooks中的登录方法
    const result = await login(email.value, password.value)
    if (result.success) {
      ElMessage.success(result.message)
      router.push('/')
    } else {
      ElMessage.error(result.message)
    }
  }
}

const handleForgotPassword = () => {
  // 点击忘记密码跳转到忘记密码页面
  router.push('/forgot-password')
}

const handleBackToEmail = () => {
  currentStep.value = 'email'
  password.value = ''
}

const handleGoogleLogin = () => {
  console.log('Google login')
}

const handleAppleLogin = () => {
  console.log('Apple login')
}

// 监听邮箱变化，重置步骤
watch(email, () => {
  if (currentStep.value !== 'email') {
    currentStep.value = 'email'
    password.value = ''
  }
})
</script>

<template>
  <div class="auth-container">
    <div class="auth-form login-form">
      <h1 class="auth-title">{{ t('login.title') }}</h1>

      <!-- 真实登录表单 -->
      <div class="real-login-form">
        <!-- 用户名/手机号输入 -->
        <div class="form-group">
          <label class="form-label">{{ t('login.userCode_label') }}</label>
          <el-input
            v-model="userCode"
            :placeholder="t('login.userCode_placeholder')"
            class="auth-input"
            size="large"
            clearable
          />
        </div>

        <!-- 密码输入 -->
        <div class="form-group">
          <div class="password-header">
            <label class="form-label">{{ t('login.password_label') }}</label>
            <a href="#" class="forgot-password" @click.prevent="handleForgotPassword">
              {{ t('login.forgot_password') }}
            </a>
          </div>
          <div class="input-wrapper">
            <el-input
              v-model="loginPassword"
              :placeholder="t('login.password_placeholder')"
              class="auth-input"
              size="large"
              :type="showLoginPassword ? 'text' : 'password'"
              clearable
            />
            <el-icon class="password-toggle" @click="showLoginPassword = !showLoginPassword">
              <Icon :icon="showLoginPassword ? 'ep:hide' : 'ep:view'" />
            </el-icon>
          </div>
        </div>

        <!-- 登录按钮 -->
        <el-button
          type="primary"
          size="large"
          class="auth-button"
          :loading="isLoading"
          :disabled="!canLogin"
          @click="handleApiLogin"
        >
          {{ t('login.login_button') }}
        </el-button>

        <!-- 注册链接 -->
        <div class="auth-footer">
          <span>{{ t('login.no_account') }}</span>
          <router-link to="/register" class="auth-link">
            {{ t('login.register_link') }}
          </router-link>
        </div>

        <!-- 社交登录 -->
        <div class="social-login">
          <div class="divider">
            <span>{{ t('login.or') }}</span>
          </div>

          <div class="social-buttons">
            <el-button class="social-button google" @click="handleGoogleLogin">
              <Icon icon="logos:google-icon" />
              {{ t('login.google') }}
            </el-button>

            <el-button class="social-button apple" @click="handleAppleLogin">
              <Icon icon="ic:baseline-apple" />
              {{ t('login.apple') }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 原有的演示登录表单（可选保留） -->
      <div class="demo-login-form" style="display: none">
        <!-- 邮箱输入 -->
        <div class="form-group">
          <label class="form-label">{{ t('login.email_label') }}</label>
          <div class="input-wrapper">
            <el-input
              v-model="email"
              :placeholder="t('login.email_placeholder')"
              class="auth-input"
              size="large"
              type="email"
              :readonly="currentStep !== 'email'"
            />
            <el-icon v-if="currentStep !== 'email'" class="edit-icon" @click="handleBackToEmail">
              <Icon icon="ep:edit" />
            </el-icon>
          </div>
        </div>

        <!-- 密码输入 (登录) -->
        <div v-if="currentStep === 'password'" class="form-group">
          <div class="password-header">
            <label class="form-label">Password</label>
            <a href="#" class="forgot-password" @click.prevent="handleForgotPassword"
              >Forgot Password?</a
            >
          </div>
          <div class="password-input-wrapper">
            <el-input
              v-model="password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="••••••••"
              class="auth-input password-input"
              size="large"
            />
            <el-icon class="password-toggle" @click="showPassword = !showPassword">
              <Icon v-if="showPassword" icon="svg-icon:show"></Icon>
              <Icon v-else icon="svg-icon:hide"></Icon>
            </el-icon>
          </div>
        </div>

        <el-button
          type="primary"
          class="auth-primary-btn"
          :class="{ disabled: !canContinue }"
          size="large"
          :loading="isLoading"
          :disabled="!canContinue"
          @click="handleContinue"
        >
          {{ currentStep === 'email' ? t('login.continue') : 'Log In' }}
        </el-button>

        <div class="auth-agreement-text">
          {{ t('login.agreement_text') }}
          <a href="#" class="link">{{ t('login.user_agreement') }}</a>
          {{ t('login.and') }}
          <a href="#" class="link">{{ t('login.privacy_policy') }}</a>
        </div>

        <div class="auth-divider">
          <span class="divider-text">{{ t('login.or') }}</span>
        </div>

        <div class="auth-social-login">
          <el-button class="auth-social-btn google-btn" type="default" @click="handleGoogleLogin">
            <Icon icon="svg-icon:google"></Icon>
            {{ t('login.continue_with_google') }}
          </el-button>

          <el-button class="auth-social-btn apple-btn" type="default" @click="handleAppleLogin">
            <Icon icon="svg-icon:apple"></Icon>
            {{ t('login.continue_with_apple') }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
// 导入公共认证样式
@use '@web/styles/auth-common.scss';

// 登录页面特有样式
.password-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .forgot-password {
    color: var(--base-color);
    text-decoration: none;
    font-size: 14px;

    &:hover {
      text-decoration: underline;
    }
  }
}

.register-link {
  text-align: center;
  font-size: 14px;
  color: #6b7280;
  margin-top: 24px;

  .link {
    color: var(--base-color);
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
