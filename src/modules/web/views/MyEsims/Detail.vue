<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import InstallationModal from '@web/views/MyEsims/Components/InstallationModal.vue'
import ImportantTips from '@web/views/MyEsims/Components/ImportantTips.vue'
import { Icon } from '@/components/Icon'
import Copyright from '@web/components/Copyright.vue'
import Footer from '@web/components/Footer.vue'
import DownloadApp from '@web/views/Home/DownloadApp.vue'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

// eSIM 详情数据
const esimDetail = ref({
  id: '',
  title: '5GB 3-Day for United States',
  data: '5GB',
  validity: '3 Days',
  planType: 'Data Only',
  expiry: '23 May 2025 | 02:54 (GMT)',
  coverage: 'United States, Canada, Mexico',
  description: [
    'The validity period starts when the eSIM connects to a mobile network in its coverage area. If you install the eSIM outside of the coverage area, you can connect to a network when you arrive.',
    'The validity period starts when the eSIM connects to a mobile network in its coverage area.',
    'If you install the eSIM outside of the coverage area, you can connect to a network when you arrive. The validity period starts when the...',
  ],
})

// installDialog
const installDialog = reactive({
  visible: false,
  deviceType: 'ios' as 'ios' | 'android',
})

// Important Tips弹窗状态
const importantTipsDialog = reactive({
  visible: false,
  pendingDeviceType: 'ios' as 'ios' | 'android',
})
// 安装选项
const installationOptions = ref([
  {
    type: 'ios',
    name: 'iOS Device',
    icon: 'apple',
  },
  {
    type: 'android',
    name: 'Android Device',
    icon: 'android',
  },
])

onMounted(() => {
  // 从路由参数获取 eSIM ID
  const esimId = route.params.id
  if (esimId) {
    esimDetail.value.id = esimId as string
    // TODO: 根据 ID 加载具体的 eSIM 数据
  }
})

const handleInstallation = (type: string) => {
  console.log('Install for:', type)
  // 首先显示Important Tips弹窗
  importantTipsDialog.pendingDeviceType = type as 'ios' | 'android'
  importantTipsDialog.visible = true
}

// 处理Important Tips确认
const handleImportantTipsConfirm = () => {
  // 关闭Important Tips弹窗，显示安装弹窗
  importantTipsDialog.visible = false
  installDialog.deviceType = importantTipsDialog.pendingDeviceType
  installDialog.visible = true
}

const goBack = () => {
  router.back()
}
</script>

<template>
  <div class="esim-detail-page">
    <div class="container-box">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">{{ esimDetail.title }}</h1>
      </div>

      <!-- 套餐详情卡片 -->
      <div class="detail-card">
        <div class="detail-grid">
          <div class="detail-item">
            <span class="detail-label">{{ t('esimDetail.data') }}</span>
            <span class="detail-value">{{ esimDetail.data }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">{{ t('esimDetail.validity') }}</span>
            <span class="detail-value">{{ esimDetail.validity }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">{{ t('myEsims.planType') }}</span>
            <span class="detail-value">{{ esimDetail.planType }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">{{ t('myEsims.expiry') }}</span>
            <span class="detail-value">{{ esimDetail.expiry }}</span>
          </div>

          <div class="detail-item full-width">
            <span class="detail-label">{{ t('myEsims.coverage') }}</span>
            <span class="detail-value">{{ esimDetail.coverage }}</span>
          </div>
        </div>
      </div>

      <!-- eSIM 安装区域 -->
      <div class="installation-section">
        <h2 class="section-title">{{ t('esimDetail.installation.title') }}</h2>

        <div class="installation-options">
          <div v-for="option in installationOptions" :key="option.type" class="installation-option">
            <div class="option-content">
              <div class="option-icon">
                <Icon
                  v-if="option.icon === 'apple'"
                  icon="svg-icon:apple-device"
                  :size="100"
                ></Icon>
                <Icon
                  v-if="option.icon === 'android'"
                  icon="svg-icon:android-device"
                  :size="100"
                ></Icon>
              </div>
              <span class="option-name">{{ option.name }}</span>
            </div>

            <el-button
              type="primary"
              class="installation-btn"
              @click="handleInstallation(option.type)"
            >
              {{ t('esimDetail.installation.button') }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 描述区域 -->
      <div class="description-section">
        <h2 class="section-title">{{ t('esimDetail.description') }}</h2>

        <div class="description-content">
          <ol class="description-list">
            <li
              v-for="(item, index) in esimDetail.description"
              :key="index"
              class="description-item"
            >
              {{ item }}
            </li>
          </ol>
        </div>
      </div>
    </div>
  </div>
  <DownloadApp />
  <Footer />
  <Copyright />

  <!-- Important Tips弹窗 -->
  <ImportantTips
    v-model:visible="importantTipsDialog.visible"
    @confirm="handleImportantTipsConfirm"
  ></ImportantTips>

  <!-- 安装弹窗 -->
  <InstallationModal
    v-model:visible="installDialog.visible"
    :device-type="installDialog.deviceType"
  >
  </InstallationModal>
</template>

<style scoped lang="scss">
.esim-detail-page {
  padding: 40px 0 80px;
}

.back-section {
  margin-bottom: 30px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 16px;
  cursor: pointer;
  padding: 8px 0;
  transition: color 0.3s ease;

  &:hover {
    color: #374151;
  }
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.detail-card {
  background: white;
  border-radius: 16px;
  padding: 40px 24px;
  border: 1px solid #e0e0e0;
  margin-bottom: 46px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 23px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &.full-width {
    grid-column: 1 / -1;
  }
}

.detail-label {
  font-size: 20px;
  color: #000000;
  width: 140px;
  font-weight: 400;
}

.detail-value {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-weight: 400;
  padding-left: 40px;
  flex: 1;
}

.installation-section,
.description-section {
  margin-bottom: 45px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 16px 0;
}

.installation-options {
  display: flex;
  gap: 16px;
}

.installation-option {
  background: white;
  border-radius: 20px;
  padding: 40px;
  border: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 50%;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 40px;
}

.option-name {
  font-size: 24px;
  font-weight: 500;
  color: #000000;
}

.installation-btn {
  transition: all 0.3s ease;
  width: 220px;
}

.description-content {
  background: white;
  border-radius: 12px;
  padding: 38px 24px;
  border: 1px solid #e0e0e0;
}

.description-list {
  margin: 0;
  color: rgba(0, 0, 0, 0.5);
  line-height: 28px;
  padding-left: 0;
}

.description-item {
  font-size: 20px;
  text-align: left;

  &:last-child {
    margin-bottom: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .esim-detail-page {
    padding: 20px 0 60px;
  }

  .page-title {
    font-size: 24px;
  }

  .detail-card {
    padding: 20px;
  }

  .detail-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;

    .detail-value {
      text-align: left;
    }
  }

  .installation-option {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .installation-btn {
    width: 100%;
  }
}
</style>
