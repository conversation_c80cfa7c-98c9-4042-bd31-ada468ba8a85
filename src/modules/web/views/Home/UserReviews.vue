<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import UserCarousel from './UserCarousel.vue'
import { Icon } from '@/components/Icon/index'
import { comments, platforms } from '@/data/comments'
import { generateStars } from '@/data/comments'

const { t } = useI18n()
</script>

<template>
  <section class="user-reviews">
    <div class="container-box">
      <h2 class="section-title">{{ t('home.user_reviews.title') }}</h2>

      <div class="reviews-content">
        <!-- 左侧平台评分 -->
        <div class="platform-ratings">
          <div v-for="platform in platforms" :key="platform.name" class="platform-rating">
            <div class="platform-logo">
              <Icon :icon="`svg-icon:${platform.logo}`" size="98"></Icon>
            </div>
            <div class="rating-info">
              <div class="rating-score">
                {{ platform.rating }}<span class="rating-max">/5</span>
              </div>
              <div class="rating-stars">
                <i
                  v-for="(isFilled, index) in generateStars(5)"
                  :key="index"
                  class="star"
                  :class="{ filled: isFilled }"
                  >★</i
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧用户评价轮播 -->
        <div class="reviews-carousel">
          <UserCarousel :list="comments" />
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped src="./UserReviews.scss"></style>
