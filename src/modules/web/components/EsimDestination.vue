<script lang="ts" setup>
import type { PopularDestination } from '@/api/Country/types'

defineProps({
  list: Array<PopularDestination>,
  default: () => [],
})

const EVENT_CLICK = 'click-cell'
const emits = defineEmits([EVENT_CLICK])

const handleClick = (item: PopularDestination) => {
  emits(EVENT_CLICK, item)
}
</script>

<template>
  <div class="destinations-grid">
    <div v-for="item in list" :key="item.iso2" class="destination-card" @click="handleClick(item)">
      <div class="destination-image">
        <img v-if="item.iconUrl" :src="item.iconUrl" :alt="item.value" />
        <p v-else class="holder">{{ (item.value || '').substring(0, 1) }}</p>
      </div>
      <div class="destination-info">
        <span class="destination-name">{{ item.value }}</span>
        <span class="destination-arrow">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9 18L15 12L9 6"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.destinations-grid {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;

  @media (max-width: 1200px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

.destination-card {
  cursor: pointer;
  display: flex;
  width: 100%;
  background-color: #fff;
  border-radius: 20px;
  padding: 15px 16px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .destination-image {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    background-color: #dddddd;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .holder {
      font-size: 22px;
      text-align: center;
      line-height: 48px;
      text-transform: uppercase;
    }
  }

  .destination-info {
    flex: 1;
    min-width: 0;
    display: flex;
    align-items: center;

    .destination-name {
      flex: 1;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .destination-arrow {
      color: #999;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
