<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import EsimShare from '@/components/EsimShare/index.vue'
import { Icon } from '@/components/Icon/index'
import EsimSearch from '@/components/EsimSearch/index.vue'
import { HEADER } from '@/const/mode'
import { LINKS_FOOTER as footerLinks, LINKS_USERS as userLinks } from '@/const/links'

interface PropType {
  mode: string
  showSearch: boolean
}

interface LinkType {
  name: string
  icon: string
  path: string
  action?: string
}

withDefaults(defineProps<PropType>(), {
  mode: HEADER.WHITE,
  showSearch: true,
})

const { t } = useI18n()
const router = useRouter()

const searchValue = ref('')

// 模拟用户登录状态 - 在实际应用中应该从store或API获取
const isLoggedIn = ref(true) // 可以切换true/false来测试不同状态
const currentUser = ref({
  name: '<PERSON>',
  email: '<EMAIL>',
  avatar: '',
})

const navItems = ref([
  { name: 'nav.shop_plans', path: '/shop-plans' },
  { name: 'nav.my_esims', path: '/my-esims' },
  { name: 'nav.about_us', path: '', dropdown: footerLinks.aboutUs },
])

const accountLinks = (userLinks as LinkType[]).concat([
  { name: 'refer.title', icon: 'svg-icon:refer', path: '/refer-earn' },
  { name: 'nav.logout', icon: 'svg-icon:logout', path: '', action: 'logout' },
])
const userMenuItems = ref(accountLinks)

const handleSearch = () => {
  console.log('Search:', searchValue.value)
}

const handleLogin = () => {
  router.push('/login')
}

const handleDropdown = (command) => {
  if (!command.url) return
  window.location.href = command.url
}

// 处理用户菜单点击
const handleUserMenuClick = (command: any) => {
  if (command.action === 'logout') {
    handleLogout()
  } else if (command.path) {
    router.push(command.path)
  }
}

// 处理登出
const handleLogout = () => {
  isLoggedIn.value = false
  currentUser.value = { name: '', email: '', avatar: '' }
  router.push('/')
  console.log('User logged out')
}
</script>

<template>
  <header :class="['header', mode]">
    <div class="header-container container-box">
      <div class="header-flex">
        <!-- Logo -->
        <div class="logo">
          <RouterLink to="/" class="logo-link">
            <img src="@/assets/images/Logo.svg" alt="logo" />
          </RouterLink>
        </div>

        <!-- Search -->
        <div v-if="showSearch" class="search-container">
          <EsimSearch v-model="searchValue" @keyup.enter="handleSearch" size="small" theme="gray" />
        </div>
      </div>

      <div class="header-flex end">
        <!-- Navigation -->
        <nav class="nav">
          <template v-for="item in navItems" :key="item.name">
            <template v-if="item.dropdown">
              <el-popover
                placement="bottom"
                popper-class="nav-popper"
                :width="280"
                trigger="click"
                :show-arrow="false"
              >
                <template #reference>
                  <span class="nav-link">
                    {{ t(item.name) }}
                    <Icon class="vb" icon="svg-icon:arrow_down" size="20"></Icon>
                  </span>
                </template>

                <template #default>
                  <div class="nav-dropdown">
                    <ul>
                      <li
                        v-for="sub in item.dropdown"
                        @click="handleDropdown(sub)"
                        class="dropdown-item"
                      >
                        <div class="dropdown-link">
                          <Icon class="dropdown-item__icon" :icon="sub.icon"></Icon>
                          <span>{{ t(sub.name) }}</span>
                        </div>
                      </li>
                    </ul>
                  </div>
                </template>
              </el-popover>
            </template>
            <RouterLink v-else :to="item.path" class="nav-link">
              {{ t(item.name) }}
            </RouterLink>
          </template>
        </nav>

        <!-- Login/User Section -->
        <div class="auth-section">
          <!-- 未登录状态 -->
          <div v-if="!isLoggedIn" class="login-section">
            <EsimShare @click="handleLogin">
              {{ t('nav.login') }}
            </EsimShare>
          </div>

          <!-- 已登录状态 -->
          <div v-else class="user-section">
            <el-popover
              placement="bottom"
              popper-class="user-popper"
              :width="280"
              trigger="click"
              :show-arrow="false"
            >
              <template #reference>
                <div class="user-info nav-link">
                  <div class="user-avatar">
                    <Icon icon="svg-icon:user" :size="28" />
                  </div>
                  <span class="user-name">{{ currentUser.name }}</span>
                  <Icon icon="svg-icon:arrow_down" :size="16" class="dropdown-arrow" />
                </div>
              </template>

              <template #default>
                <div class="user-dropdown">
                  <ul>
                    <template v-for="item in userMenuItems" :key="item.name">
                      <li @click="handleUserMenuClick(item)" class="dropdown-item">
                        <div class="dropdown-link">
                          <Icon class="dropdown-item__icon" :icon="item.icon"></Icon>
                          <span>{{ t(item.name) }}</span>
                        </div>
                      </li>
                    </template>
                  </ul>
                </div>
              </template>
            </el-popover>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<style scoped lang="scss">
.header {
  height: var(--header-height);
  z-index: 10;

  &.white {
    background: #ffffff;
    border-bottom: 1px solid #e0e0e0;
  }

  &.transparent {
    background: transparent;
    border-bottom: 1px solid transparent;
  }

  .header-container {
    display: flex;
    align-items: center;
    height: 100%;

    .header-flex {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;

      &.end {
        justify-content: flex-end;
      }
    }
  }

  .logo {
    margin-right: 45px;

    .logo-link {
      font-size: 24px;
      font-weight: bold;
      color: #1f2937;
      text-decoration: none;
      height: var(--logo-height);
      line-height: var(--logo-height);

      img {
        height: var(--logo-height);
      }

      &:hover {
        color: #059669;
      }
    }
  }

  .search-container {
    flex: 1;
    margin-left: 45px;
    max-width: 460px;
  }

  .nav {
    display: flex;
    align-items: center;
    gap: 60px;

    .nav-link {
      color: var(--primary-color);
      text-decoration: none;
      transition: color 0.2s;
      font-size: 20px;
      line-height: 40px;
      white-space: nowrap;
      font-weight: bold;
      cursor: pointer;

      &:hover {
        color: var(--base-color);
      }

      &.router-link-active {
        color: var(--base-color);
      }
    }

    .vb {
      vertical-align: middle;
    }
  }

  .auth-section {
    margin-left: 60px;
  }

  .login-section {
    // 保持原有样式
  }

  .user-section {
    .user-info {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 8px 12px;
      border-radius: 8px;
      transition: background-color 0.2s;
      cursor: pointer;

      &:hover {
        .user-name,
        .dropdown-arrow {
          color: var(--base-color);
        }
      }
    }

    .user-avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333333;
    }

    .user-name {
      font-size: 20px;
      font-weight: 500;
      color: var(--primary-color);
    }

    .dropdown-arrow {
      color: var(--primary-color);
      transition: transform 0.2s;
    }
  }
}
</style>

<style lang="scss">
// 导航下拉菜单样式
.nav-popper.el-popper {
  --el-popover-padding: 0;
  --el-popover-border-radius: 12px;
  --el-box-shadow-light: 0px 0px 16px 0px rgba(0, 0, 0, 0.2);
  border: none;
}

.nav-dropdown,
.user-dropdown {
  .dropdown-item {
    padding-left: 20px;
    padding-right: 16px;
    font-size: 20px;
    line-height: 64px;
    cursor: pointer;

    &:not(:first-child) {
      .dropdown-link {
        border-top: 1px solid #e0e0e0;
      }
    }

    &:hover {
      background-color: rgba(0, 198, 94, 0.06);
    }

    &__icon {
      margin-right: 16px;
    }
  }

  .dropdown-link {
    display: flex;
    align-items: center;
    padding: 0 16px;
  }
}

.user-dropdown {
}

// 用户下拉菜单样式
.user-popper.el-popper {
  --el-popover-padding: 8px 0;
  --el-popover-border-radius: 12px;
  --el-box-shadow-light: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
  border: none;
}
</style>
