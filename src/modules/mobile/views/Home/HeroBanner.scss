// 移动端HeroBanner样式将在这里添加
.hero-container {
  width: 100%;
  position: relative;
  height: 375px;
  background: #cbcbcb url('../../images/BANNER_top_bg.png') center bottom no-repeat;
  background-size: 280px;
  border-radius: 0 0 30px 30px;
}

.hero-img {
  padding: 0 10px;
  img {
    width: 100%;
  }
}

.hero-content {
  padding: 48px 16px 0;
  text-align: center;
}

.hero-title {
  color: #000000;
  font-size: 36px;
  line-height: 48px;
  font-weight: bold;
  margin-bottom: 37.5px;
}

.hero-highlight {
  color: #156326;
}

.promo-container {
  margin-top: -30px;
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.promo-box {
  height: 72px;
  padding: 16px;
  color: #ffffff;
  border-radius: 12px;
  font-size: 14px;
  margin-left: 16px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &.adventure {
    background-color: #f68a8a;

    .promo-button {
      color: #ff6060;
    }
  }

  &.share {
    background-color: #f1bf86;

    .promo-button {
      color: #f1bf86;

      .esim-icon {
        color: #fff;
        background: #f1bf86;
        font-size: 16px;
        border-radius: 50%;
      }
    }
  }
}

.promo-text {
  line-height: 20px;
}

.promo-button {
  flex: 0 0 auto;
  border-radius: 8px;
  padding: 10px 22px;
  line-height: 1;
  background: #ffffff;

  .esim-icon {
    font-size: 16px;
    vertical-align: text-bottom;
    margin-left: 3px;
  }
}
