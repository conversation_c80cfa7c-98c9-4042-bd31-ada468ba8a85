<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import EsimSlider from '@/components/EsimSlider/index.vue'
import EsimShare from '@/components/EsimShare/index.vue'
import EsimDestination from '@mobile/components/EsimDestination.vue'
import { destinationTypes } from '@/data/destinations'
import { useDestinations, useDestinationNavigation } from '@/hooks/useDestinations'
import type { PopularDestination } from '@/api/Country/types'
import { watchEffect, ref } from 'vue'

const { t } = useI18n()

// 使用目的地数据 hooks
const { getDestinationsByLimit, setActiveTab } = useDestinations()

const list = ref<PopularDestination[]>([])
watchEffect(() => {
  list.value = getDestinationsByLimit(4)
})

// 使用目的地导航 hooks
const { goToPlanDetail, goToShopPlans } = useDestinationNavigation()

// 点击套餐信息
const handleClickCell = (item: any) => {
  goToPlanDetail(item)
}

// 处理查看更多
const handleShowMore = () => {
  goToShopPlans()
}

// 处理标签切换
const handleTabChange = (item) => {
  setActiveTab(item.value)
}
</script>

<template>
  <section class="popular-destinations">
    <div class="container">
      <h2 class="section-title">{{ t('home.popular.title') }}</h2>

      <!-- 使用Vant的Tabs组件实现标签切换 -->
      <EsimSlider
        size="mobile"
        :list="destinationTypes.map((item) => ({ label: t(item.label), value: item.value }))"
        @change="handleTabChange"
      >
      </EsimSlider>

      <!-- 目的地网格 -->
      <EsimDestination :list="list" @click-cell="handleClickCell"> </EsimDestination>

      <!-- 显示更多按钮 -->
      <div class="show-more-container">
        <EsimShare size="medium" @click="handleShowMore">Show More</EsimShare>
      </div>
    </div>
  </section>
</template>

<style src="./PopularDestinations.scss" lang="scss" scoped></style>
