<script setup lang="ts">
import { Swipe as <PERSON><PERSON>wi<PERSON>, SwipeItem as VanSwipeItem } from 'vant'
import { generateStars } from '@/data/comments'
defineProps({
  list: {
    type: Array<any>,
    default: () => [],
  },
})
</script>

<template>
  <van-swipe :loop="false" :width="300" indicator-color="#000000">
    <van-swipe-item v-for="item in list" :key="item.id">
      <div class="review-card no-rem">
        <div class="review-header">
          <div class="user-avatar">
            <img :src="item.avatar" :alt="item.name" />
          </div>
          <div class="user-info">
            <div class="rating-stars">
              <i
                v-for="(isFilled, index) in generateStars(item.rating)"
                :key="index"
                class="star"
                :class="{ filled: isFilled }"
                >★</i
              >
            </div>
            <p class="review-date no-wrap">{{ item.name }}</p>
          </div>
        </div>
        <div class="review-content">
          <h3 class="review-title">{{ item.title }}</h3>

          <p>{{ item.content }}</p>
        </div>
      </div>
    </van-swipe-item>
  </van-swipe>
</template>

<style lang="scss" scoped>
// 基本样式，详细样式将在后续添加
.review-card {
  padding: 16px;
  background: var(--vt-c-white);
  border-radius: 8px;
  margin: 0 8px;

  &.no-rem {
    height: 236px;
    overflow: hidden;
  }
}

.review-header {
  display: flex;
  margin-bottom: 16px;
}

.review-title {
  color: var(--primary-color);
  font-weight: bold;
  margin-bottom: 12px;
}

.review-content {
  color: var(--vt-c-text-light-2);
  font-size: 12px;
  line-height: 16px;

  p {
    word-break: break-all;
  }
}

.user-avatar {
  width: 48px;
  height: 48px;
  background-color: #ddd;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.user-info {
  color: var(--vt-c-text-light-2);
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.star {
  color: #333333;

  & + & {
    margin-left: 2px;
  }

  // &.filled {
  //   color: #ffd700;
  // }
}
</style>
