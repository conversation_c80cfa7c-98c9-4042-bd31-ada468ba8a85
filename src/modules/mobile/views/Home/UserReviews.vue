<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import UserCarousel from './UserCarousel.vue'
import { getImageUrl } from '@/utils/getImageUrl'
import { comments, platforms, generateStars } from '@/data/comments'

const { t } = useI18n()
</script>

<template>
  <section class="user-reviews">
    <h2 class="section-title">{{ t('home.user_reviews.title') }}</h2>

    <!-- 平台评分 -->
    <div class="platform-ratings">
      <div v-for="platform in platforms" :key="platform.name" class="platform-rating">
        <div class="platform-logo">
          <img :src="getImageUrl(`${platform.logo}.svg`, 'svgs')" :alt="platform.name" />
        </div>
        <div class="rating-info">
          <div class="rating-score">{{ platform.rating }}<span class="rating-max">/5</span></div>
          <div class="rating-stars">
            <i
              v-for="(isFilled, index) in generateStars(5)"
              :key="index"
              class="star"
              :class="{ filled: isFilled }"
              >★</i
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 用户评价轮播 -->
    <div class="reviews-carousel">
      <UserCarousel :list="comments" />
    </div>
  </section>
</template>

<style src="./UserReviews.scss" lang="scss" scoped></style>
