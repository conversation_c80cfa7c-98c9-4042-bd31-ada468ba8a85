// 移动端HowItWorks样式将在这里添加
.how-it-works {
  margin-top: 20px;
  padding: 36px 0;
  background-color: #ffffff;
}

.section-title {
  color: #000000;
  font-size: 24px;
  line-height: 1;
  text-align: center;
  margin-bottom: 12px;
  font-weight: bold;
}

.workflow-step {
  text-align: center;
}

.phone-container {
  padding: 24px 0;
  .phone-image {
    width: 184px;
    height: 375px;
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    display: block;
    margin: auto;
    background: #ddd;
    border-radius: 12px;
  }
}

.step-indicator {
  display: inline-block;
  margin-top: 12px;
  padding: 7px 12px;
  background-color: #e8e8e8;
  border-radius: 28px;
  .step-number {
    text-align: center;
    color: #000000;
    font-size: 10px;
  }
}

.step-title {
  margin-top: 24px;
  color: #333333;
  font-size: 16px;
  line-height: 1;
}

.step-description {
  margin-top: 12px;
  padding: 0 24px;
  font-size: 14px;
  line-height: 20px;
  color: var(--vt-c-text-light-2);
}
