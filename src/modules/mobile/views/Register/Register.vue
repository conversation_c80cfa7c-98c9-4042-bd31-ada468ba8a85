<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuth } from '@/hooks/useAuth'
import { showToast, showLoadingToast, closeToast } from 'vant'

const { t } = useI18n()
const router = useRouter()

const {
  email,
  password,
  retypePassword,
  isLoading,
  showPassword,
  showRetypePassword,
  emailValid,
  passwordValid,
  passwordsMatch,
  passwordError,
  canContinueToCountry,
  checkUserExists,
  register,
  selectedCountry,
} = useAuth()

// 处理继续到地区选择
const handleContinue = async () => {
  if (!canContinueToCountry.value) return

  // 检查邮箱是否已存在
  showLoadingToast({
    message: t('common.loading'),
    forbidClick: true,
  })

  try {
    const userExists = await checkUserExists(email.value)

    if (userExists) {
      closeToast()
      showToast(t('register.user_exists'))
      setTimeout(() => {
        router.push('/mobile/#/login')
      }, 1500)
      return
    }

    // 使用原有的注册方法进行注册
    const result = await register(email.value, password.value, selectedCountry.value)
    closeToast()

    if (result.success) {
      showToast(result.message)
      // 注册成功后跳转到登录页
      setTimeout(() => {
        router.push('/mobile/#/login')
      }, 1500)
    } else if (result.shouldRedirectToLogin) {
      showToast(result.message)
      setTimeout(() => {
        router.push('/mobile/#/login')
      }, 1500)
    } else {
      showToast(result.message)
    }
  } catch (error) {
    closeToast()
    showToast(t('common.error'))
  }
}
</script>

<template>
  <div class="auth-container">
    <h1 class="login-title">{{ t('register.title') }}</h1>
    <div class="auth-form">
      <!-- 表单字段 -->
      <van-cell-group inset>
        <van-field
          v-model="email"
          :label="t('login.email_label')"
          :placeholder="t('login.email_placeholder')"
          type="email"
          :error-message="t('register.email_error')"
        />

        <van-field
          v-model="password"
          :label="t('register.password_label')"
          :type="showPassword ? 'text' : 'password'"
          :placeholder="t('register.password_placeholder')"
          :right-icon="showPassword ? 'eye-o' : 'closed-eye'"
          :error-message="t('register.password_error')"
          @click-right-icon="showPassword = !showPassword"
        />

        <van-field
          v-model="retypePassword"
          :label="t('register.confirm_password_label')"
          :type="showRetypePassword ? 'text' : 'password'"
          :placeholder="t('register.confirm_password_placeholder')"
          :right-icon="showRetypePassword ? 'eye-o' : 'closed-eye'"
          :error-message="t('register.password_mismatch')"
          @click-right-icon="showRetypePassword = !showRetypePassword"
        />
      </van-cell-group>

      <!-- 注册按钮 -->
      <div class="auth-button-group">
        <van-button
          type="primary"
          size="large"
          block
          round
          class="auth-primary-btn"
          :loading="isLoading"
          :disabled="!canContinueToCountry"
          @click="handleContinue"
        >
          {{ t('login.continue') }}
        </van-button>
      </div>
      <!-- 协议文本 -->
      <div class="auth-agreement-text">
        {{ t('login.agreement_text') }}
        <span class="link">{{ t('login.user_agreement') }}</span>
        {{ t('login.and') }}
        <span class="link">{{ t('login.privacy_policy') }}</span>
      </div>
      <!-- 密码要求提示 -->
      <div class="auth-requirements">
        Password must be 6-20 characters long and include at least two of the following: letters,
        numbers, or symbols. It also cannot be one you’ve used before.
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
// 注册页面特有样式（如果有的话）
// 所有通用样式已移至公共样式文件 auth-common.scss
@use '@mobile/styles/auth-common.scss';
</style>
