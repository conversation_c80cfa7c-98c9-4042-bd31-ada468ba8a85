<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuth } from '@/hooks/useAuth'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { countries, getCountryName } from '@/data/countries'

const { t } = useI18n()
const router = useRouter()

// 国家选择器状态
const showCountryPicker = ref(false)
const countryOptions = computed(() =>
  countries.map(country => ({
    text: getCountryName(country, t),
    value: country.code,
    flag: country.flag
  }))
)

// 国家选择确认
const onCountryConfirm = ({ selectedValues }) => {
  setSelectedCountry(selectedValues[0])
  showCountryPicker.value = false
}

const {
  email,
  password,
  retypePassword,
  isLoading,
  showPassword,
  showRetypePassword,
  emailValid,
  passwordValid,
  passwordsMatch,
  passwordError,
  canContinueToCountry,
  canSubmitRegister,
  checkUserExists,
  register,
  selectedCountry,
  registerStep,
  continueToCountrySelection,
  backToRegisterForm,
  setSelectedCountry,
} = useAuth()

// 处理继续到地区选择
const handleContinue = async () => {
  if (!canContinueToCountry.value) return

  // 检查邮箱是否已存在
  showLoadingToast({
    message: t('common.loading'),
    forbidClick: true,
  })

  try {
    const userExists = await checkUserExists(email.value)

    if (userExists) {
      closeToast()
      showToast(t('register.user_exists'))
      setTimeout(() => {
        router.push('/mobile/#/login')
      }, 1500)
      return
    }

    closeToast()

    // 切换到国家选择步骤
    continueToCountrySelection()
  } catch (error) {
    closeToast()
    showToast(t('common.error'))
  }
}

// 处理注册
const handleRegister = async () => {
  if (!canSubmitRegister.value) return

  showLoadingToast({
    message: t('register.registering'),
    forbidClick: true,
  })

  const result = await register(email.value, password.value, selectedCountry.value)
  closeToast()

  if (result.success) {
    showToast(result.message || t('register.register_success'))
    // 注册成功后跳转到登录页面
    setTimeout(() => {
      router.push('/mobile/#/login')
    }, 1500)
  } else {
    showToast(result.message || t('register.register_failed'))
    if (result.shouldRedirectToLogin) {
      setTimeout(() => {
        router.push('/mobile/#/login')
      }, 1500)
    }
  }
}
</script>

<template>
  <div class="auth-container">
    <!-- 注册表单步骤 -->
    <div v-if="registerStep === 'form'" class="register-form-step">
      <h1 class="login-title">{{ t('register.title') }}</h1>
      <div class="auth-form">
      <!-- 表单字段 -->
      <van-cell-group inset>
        <van-field
          v-model="email"
          :label="t('login.email_label')"
          :placeholder="t('login.email_placeholder')"
          type="email"
          :error-message="t('register.email_error')"
        />

        <van-field
          v-model="password"
          :label="t('register.password_label')"
          :type="showPassword ? 'text' : 'password'"
          :placeholder="t('register.password_placeholder')"
          :right-icon="showPassword ? 'eye-o' : 'closed-eye'"
          :error-message="t('register.password_error')"
          @click-right-icon="showPassword = !showPassword"
        />

        <van-field
          v-model="retypePassword"
          :label="t('register.confirm_password_label')"
          :type="showRetypePassword ? 'text' : 'password'"
          :placeholder="t('register.confirm_password_placeholder')"
          :right-icon="showRetypePassword ? 'eye-o' : 'closed-eye'"
          :error-message="t('register.password_mismatch')"
          @click-right-icon="showRetypePassword = !showRetypePassword"
        />
      </van-cell-group>

      <!-- 注册按钮 -->
      <div class="auth-button-group">
        <van-button
          type="primary"
          size="large"
          block
          round
          class="auth-primary-btn"
          :loading="isLoading"
          :disabled="!canContinueToCountry"
          @click="handleContinue"
        >
          {{ t('login.continue') }}
        </van-button>
      </div>
      <!-- 协议文本 -->
      <div class="auth-agreement-text">
        {{ t('login.agreement_text') }}
        <span class="link">{{ t('login.user_agreement') }}</span>
        {{ t('login.and') }}
        <span class="link">{{ t('login.privacy_policy') }}</span>
      </div>
      <!-- 密码要求提示 -->
      <div class="auth-requirements">
        Password must be 6-20 characters long and include at least two of the following: letters,
        numbers, or symbols. It also cannot be one you’ve used before.
      </div>
    </div>

    <!-- 国家选择步骤 -->
    <div v-else-if="registerStep === 'country'" class="country-selection-step">
      <div class="country-selection-header">
        <van-nav-bar
          :title="t('register.select_residence')"
          left-arrow
          @click-left="backToRegisterForm"
        />
      </div>

      <div class="auth-form">
        <!-- 国家选择 -->
        <van-cell-group inset>
          <van-field
            v-model="selectedCountry"
            :label="t('register.country_region')"
            :placeholder="t('register.select_country_placeholder')"
            readonly
            is-link
            @click="showCountryPicker = true"
          />
        </van-cell-group>

        <!-- 国家选择器 -->
        <van-popup v-model:show="showCountryPicker" position="bottom">
          <van-picker
            :columns="countryOptions"
            @confirm="onCountryConfirm"
            @cancel="showCountryPicker = false"
          />
        </van-popup>

        <!-- 注册按钮 -->
        <div class="auth-button-group">
          <van-button
            type="primary"
            size="large"
            block
            round
            class="auth-primary-btn"
            :loading="isLoading"
            :disabled="!canSubmitRegister"
            @click="handleRegister"
          >
            {{ t('register.sign_up') }}
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
// 注册页面特有样式（如果有的话）
// 所有通用样式已移至公共样式文件 auth-common.scss
@use '@mobile/styles/auth-common.scss';
</style>
