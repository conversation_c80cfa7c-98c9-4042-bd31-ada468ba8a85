<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import EsimSlider from '@/components/EsimSlider/index.vue'
import EsimStages from '@/components/EsimStages/index.vue'
import EsimDestination from '../../components/EsimDestination.vue'
import { destinationTypes } from '@/data/destinations'
import { useDestinations, useDestinationNavigation } from '@/hooks/useDestinations'

const { t } = useI18n()

// 使用目的地数据 hooks
const { loading, error, filteredDestinations, setActiveTab, setLetterFilter } = useDestinations()

// 使用目的地导航 hooks
const { goToPlanDetail } = useDestinationNavigation()

// 处理目的地点击
const handleTo = (item: any) => {
  goToPlanDetail(item)
}

// 处理标签切换
const handleTabChange = (item) => {
  setActiveTab(item.value)
}

// 处理过滤器变化
const handleFilter = function (item: any) {
  // 根据选中的字母范围过滤数据
  console.log('选中的字母范围:', item.label, item.value)
  setLetterFilter(item.value || '')
}
</script>

<template>
  <section class="shop-plans">
    <h2 class="section-title">{{ t('home.popular.title') }}</h2>

    <EsimSlider
      size="mobile"
      :list="destinationTypes.map((item) => ({ label: t(item.label), value: item.value }))"
      @change="handleTabChange"
    >
    </EsimSlider>

    <div class="stages-wrapper">
      <EsimStages size="mobile" :border="false" @change="handleFilter"></EsimStages>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-message">
      <p>加载数据时出错，请稍后重试</p>
    </div>

    <!-- 目的地列表 -->
    <EsimDestination v-else :list="filteredDestinations" @click-cell="handleTo"> </EsimDestination>

    <!-- 无数据提示 -->
    <div v-if="!loading && !error && filteredDestinations.length === 0" class="no-data">
      <p>暂无数据</p>
    </div>
  </section>
</template>

<style lang="scss" scoped>
.shop-plans {
  padding: 0 16px;
  margin-top: 36px;
  margin-bottom: 46px;
}

.section-title {
  padding: 24px 0 36px;
  font-size: 24px;
  line-height: 1;
  font-weight: bold;
  text-align: center;
  color: #000000;
}

.stages-wrapper {
  margin-top: 24px;
  margin-bottom: 12px;
}

.error-message {
  text-align: center;
  padding: 40px 20px;
  color: #f56565;
  background-color: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  margin: 20px 0;

  p {
    margin: 0;
    font-size: 16px;
  }
}

.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #718096;

  p {
    margin: 0;
    font-size: 16px;
  }
}
</style>
