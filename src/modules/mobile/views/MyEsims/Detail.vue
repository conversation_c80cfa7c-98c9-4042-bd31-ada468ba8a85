<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import PageHeader from '@mobile/components/HeaderTitle.vue'
import ImportantTips from '@mobile/components/ImportantTips.vue'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

// eSIM 详情数据
const esimDetail = ref({
  id: '',
  title: '5GB 3-Day for United States',
  data: '5GB',
  validity: '3 Days',
  planType: 'Data Only',
  expiry: '23 May 2025 | 02:54 (GMT)',
  coverage: 'United States, Canada, Mexico',
  status: 'Active',
  description: [
    'The validity period starts when the eSIM connects to a mobile network in its coverage area. If you install the eSIM outside of the coverage area, you can connect to a network when you arrive.',
    'The validity period starts when the eSIM connects to a mobile network in its coverage area.',
    'If you install the eSIM outside of the coverage area, you can connect to a network when you arrive. The validity period starts when the eSIM connects to a mobile network.',
  ],
})

// Coverage 展开状态
const showCoverage = ref(false)

// ImportantTips 弹窗状态
const showImportantTips = ref(false)

// 安装选项
const installationOptions = ref([
  {
    type: 'ios',
    name: 'iOS Device',
    icon: 'apple',
  },
  {
    type: 'android',
    name: 'Android Device',
    icon: 'android',
  },
])

// 切换Coverage显示
const toggleCoverage = () => {
  showCoverage.value = !showCoverage.value
}

onMounted(() => {
  // 从路由参数获取 eSIM ID
  const esimId = route.params.id
  if (esimId) {
    esimDetail.value.id = esimId as string
  }
})

// 处理安装
const handleInstallation = () => {
  // 显示ImportantTips弹窗
  showImportantTips.value = true
}

// 处理ImportantTips确认
const handleImportantTipsConfirm = () => {
  // 跳转到安装指令页面
  router.push({
    name: 'installation-instruction',
    params: { id: esimDetail.value.id },
  })
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'Active':
      return '#00C851'
    case 'Expired':
      return '#FF4757'
    default:
      return '#6B7280'
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<template>
  <div class="esim-detail-page">
    <!-- 页面标题 -->
    <PageHeader :title="esimDetail.title" show-back @back="goBack" />

    <!-- eSIM 基本信息 -->
    <div class="esim-info-section">
      <!-- 详细信息 -->
      <van-cell :title="t('esimDetail.data')" :value="esimDetail.data" class="detail-cell" />
      <van-cell
        :title="t('esimDetail.validity')"
        :value="esimDetail.validity"
        class="detail-cell"
      />
      <van-cell :title="t('myEsims.planType')" :value="esimDetail.planType" class="detail-cell" />
      <van-cell :title="t('myEsims.expiry')" :value="esimDetail.expiry" class="detail-cell" />

      <!-- Coverage 可折叠 -->
      <van-cell
        :title="t('myEsims.coverage')"
        class="detail-cell coverage-cell"
        is-link
        @click="toggleCoverage"
      >
        <template #right-icon>
          <van-icon :name="showCoverage ? 'arrow-up' : 'arrow-down'" />
        </template>
      </van-cell>

      <!-- Coverage 内容 -->
      <van-cell v-show="showCoverage" class="coverage-content">
        <template #default>
          <div class="coverage-text">{{ esimDetail.coverage }}</div>
        </template>
      </van-cell>
    </div>

    <!-- eSIM 安装区域 -->
    <div class="installation-section">
      <div class="section-title">{{ t('esimDetail.installation.title') }}</div>

      <!-- 安装指令按钮 -->
      <van-cell
        :title="t('esimDetail.installation.instruction')"
        class="installation-cell"
        is-link
        @click="handleInstallation('general')"
      >
        <template #icon>
          <div class="installation-icon">
            <van-icon name="guide-o" size="24" />
          </div>
        </template>
      </van-cell>
    </div>

    <!-- 描述区域 -->
    <div class="description-section">
      <div class="section-title">{{ t('esimDetail.description') }}</div>

      <van-cell-group inset class="description-card">
        <van-cell class="description-content">
          <template #default>
            <ol class="description-list">
              <li
                v-for="(item, index) in esimDetail.description"
                :key="index"
                class="description-item"
              >
                {{ item }}
              </li>
            </ol>
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- Important Tips 弹窗 -->
    <ImportantTips v-model:visible="showImportantTips" @confirm="handleImportantTipsConfirm" />
  </div>
</template>

<style lang="scss" scoped>
.esim-detail-page {
  min-height: 100vh;
  padding: 0 16px;
  background-color: var(--vt-c-white-soft);
}

.esim-info-section {
  background: white;
  padding: 0;
}

.installation-section {
  margin-top: 16px;
  background: white;
  padding: 0;
}

.description-section {
  margin-top: 16px;
  background: white;
  margin-bottom: 60px;
  padding: 0;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  padding: 16px;
  margin: 0;
  background: white;
}

// 详细信息单元格
.detail-cell {
  :deep(.van-cell__title) {
    font-size: 16px;
    color: #323233;
    font-weight: 400;
  }

  :deep(.van-cell__value) {
    font-size: 16px;
    color: #969799;
    text-align: right;
  }
}

// Coverage 单元格
.coverage-cell {
  :deep(.van-cell__right-icon) {
    margin-left: 8px;
  }
}

.coverage-content {
  background: white;

  .coverage-text {
    font-size: 14px;
    color: #646566;
    line-height: 1.5;
    padding: 8px 0;
  }
}

// 安装区域
.installation-cell {
  :deep(.van-cell__title) {
    font-size: 16px;
    color: #323233;
    font-weight: 400;
  }

  :deep(.van-cell__icon) {
    margin-right: 12px;
  }
}

.installation-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #f7f8fa;
  border-radius: 8px;
}

// 描述区域
.description-card {
  :deep(.van-cell-group) {
    --van-cell-group-inset-radius: 12px;
  }
}

.description-content {
  :deep(.van-cell__value) {
    flex: 1;
  }
}

.description-list {
  margin: 0;
  color: rgba(0, 0, 0, 0.5);
  text-align: left;
  line-height: 1.6;
}

.description-item {
  margin-bottom: 12px;
  font-size: 14px;

  &:last-child {
    margin-bottom: 0;
  }
}

// Vant 组件样式覆盖
:deep(.van-cell) {
  --van-cell-vertical-padding: 16px;
  --van-cell-horizontal-padding: 16px;
  --van-cell-border-color: #ebedf0;
}
</style>
