<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant'
import PageHeader from '@mobile/components/HeaderTitle.vue'
import { Icon } from '@/components/Icon'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

// 设备类型
const deviceType = ref('android') // 默认Android
const installMethod = ref('qr') // qr 或 manual

// 设备选项
const deviceOptions = [
  { value: 'android', label: 'Samsung' },
  { value: 'ios', label: 'Google Pixel' },
]

// 安装方法选项
const installMethods = [
  { value: 'qr', label: 'QR Code' },
  { value: 'manual', label: 'Manual' },
]

// 模拟QR码数据
const qrCodeData = 'LPA:1$rsp-prod.oberthur.net$04-78945612300000000000000000000001'

// 获取当前设备的安装步骤
const installSteps = computed(() => {
  if (deviceType.value === 'android') {
    return {
      step1: {
        title: t('installation.android.step1.title'),
        instructions: [
          t('installation.android.step1.instruction1'),
          t('installation.android.step1.instruction2'),
          t('installation.android.step1.instruction3'),
        ],
      },
      step2: {
        title: t('installation.android.step2.title'),
        instructions: [
          t('installation.android.step2.instruction1'),
          t('installation.android.step2.instruction2'),
          t('installation.android.step2.instruction3'),
        ],
      },
    }
  } else {
    return {
      step1: {
        title: t('installation.ios.step1.title'),
        instructions: [
          t('installation.ios.step1.instruction1'),
          t('installation.ios.step1.instruction2'),
          t('installation.ios.step1.instruction3'),
          t('installation.ios.step1.instruction4'),
          t('installation.ios.step1.instruction5'),
          t('installation.ios.step1.instruction6'),
          t('installation.ios.step1.instruction7'),
        ],
      },
      step2: {
        title: t('installation.ios.step2.title'),
        instructions: [
          t('installation.ios.step2.instruction1'),
          t('installation.ios.step2.instruction2'),
        ],
      },
    }
  }
})

// 处理设备切换
const handleDeviceChange = (device: string) => {
  deviceType.value = device
}

// 处理安装方法切换
const handleMethodChange = (method: string) => {
  installMethod.value = method
}

// 保存为图片
const handleSaveImage = () => {
  showToast(t('installation.saveImageSuccess'))
}

// 复制激活码
const handleCopyCode = async () => {
  try {
    await navigator.clipboard.writeText(qrCodeData)
    showToast(t('installation.copySuccess'))
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = qrCodeData
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    showToast(t('installation.copySuccess'))
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<template>
  <div class="installation-page">
    <!-- 页面标题 -->
    <PageHeader
      :title="`${deviceType === 'android' ? 'Android' : 'iOS'} Device`"
      show-back
      @back="goBack"
    />

    <div class="installation-content">
      <!-- 安装方法选择 -->
      <div class="method-selector">
        <van-button
          v-for="method in installMethods"
          :key="method.value"
          :type="installMethod === method.value ? 'primary' : 'default'"
          :class="['method-btn', { active: installMethod === method.value }]"
          @click="handleMethodChange(method.value)"
        >
          {{ method.label }}
        </van-button>
      </div>

      <!-- 警告信息 -->
      <div class="warning-box">
        <div class="warning-text">
          <span class="warning-label">{{ t('installation.warning.label') }}</span>
          {{ t('installation.warning.message') }}
        </div>
      </div>

      <!-- 设备选择 -->
      <div class="device-selector">
        <div v-for="device in deviceOptions" :key="device.value" class="device-item">
          <div
            :class="['device-card', { active: deviceType === device.value }]"
            @click="handleDeviceChange(device.value)"
          >
            <div class="device-info">
              <span class="device-name">{{ device.label }}</span>
            </div>
            <div v-if="deviceType === device.value" class="device-indicator"></div>
          </div>
        </div>
      </div>

      <!-- QR码区域 -->
      <div v-if="installMethod === 'qr'" class="qr-section">
        <div class="qr-container">
          <!-- 这里应该是实际的QR码组件，暂时用占位符 -->
          <div class="qr-placeholder">
            <div class="qr-code">
              <!-- QR码占位符 -->
              <div class="qr-pattern"></div>
            </div>
          </div>
        </div>

        <div class="qr-description">
          {{ t('installation.qrDescription') }}
        </div>

        <!-- 保存按钮 -->
        <van-button type="primary" block round class="save-btn" @click="handleSaveImage">
          {{ t('installation.saveAsImage') }}
        </van-button>
      </div>

      <!-- 手动安装区域 -->
      <div v-else class="manual-section">
        <div class="manual-code">
          <div class="code-header">
            <span class="code-label">{{ t('installation.manualCode.label') }}</span>
          </div>
          <div class="code-container">
            <div class="code-value">{{ qrCodeData }}</div>
            <van-button class="copy-btn" @click="handleCopyCode">
              <Icon icon="svg-icon:copy"></Icon>
            </van-button>
          </div>
        </div>
        <div class="code-description">
          {{ t('installation.manualCode.description') }}
        </div>
      </div>

      <!-- 安装步骤 -->
      <div class="steps-section">
        <!-- Step 1 -->
        <div class="step-item">
          <div class="step-title">{{ installSteps.step1.title }}</div>
          <ol class="step-instructions">
            <li
              v-for="(instruction, index) in installSteps.step1.instructions"
              :key="index"
              class="instruction-item"
            >
              {{ instruction }}
            </li>
          </ol>
        </div>

        <!-- Step 2 -->
        <div class="step-item">
          <div class="step-title">{{ installSteps.step2.title }}</div>
          <ol class="step-instructions">
            <li
              v-for="(instruction, index) in installSteps.step2.instructions"
              :key="index"
              class="instruction-item"
            >
              {{ instruction }}
            </li>
          </ol>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.installation-page {
  min-height: 100vh;
  background: #f7f8fa;
}

.installation-content {
  padding: 16px;
  background-color: white;
}

// 方法选择器
.method-selector {
  display: flex;
  border-radius: 12px;
  gap: 8px;
  border: 1px solid #e0e0e0;
  padding: 4px;
}

.method-btn {
  flex: 1;
  height: 40px;
  border-radius: 8px;

  &.active {
    background: #323233;
    border-color: #323233;
    color: white;
  }

  &:not(.active) {
    background: white;
    border: none;
    color: #646566;
  }
}

// 警告框
.warning-box {
  padding: 0;
  margin-bottom: 24px;
}

.warning-text {
  font-size: 12px;
  color: #ff4757;
  margin-top: 16px;
  line-height: 1.5;
  text-align: left;
}
// 设备选择器
.device-selector {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.device-item {
  flex: 1;
}

.device-card {
  background: white;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &.active {
    .device-name {
      color: #00c851;
      font-weight: 600;
    }
  }

  &:not(.active) {
    .device-name {
      color: #323233;
    }
  }
}

.device-info {
  text-align: center;
  margin-bottom: 8px;
}

.device-name {
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.device-indicator {
  width: 30%;
  height: 4px;
  background: #00c851;
  border-radius: 2px;
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
}

// QR码区域
.qr-section {
  text-align: center;
}

.qr-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 16px;
}

.qr-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
}

.qr-code {
  width: 200px;
  height: 200px;
  background: white;
  border: 2px solid #ebedf0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-pattern {
  width: 180px;
  height: 180px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect width="100" height="100" fill="white"/><rect x="10" y="10" width="10" height="10" fill="black"/><rect x="30" y="10" width="10" height="10" fill="black"/><rect x="50" y="10" width="10" height="10" fill="black"/><rect x="70" y="10" width="10" height="10" fill="black"/></svg>')
    center/cover;
}

.qr-description {
  font-size: 14px;
  color: #646566;
  line-height: 1.5;
  text-align: left;
  margin-bottom: 24px;
}

.save-btn {
  height: 48px;
  background: #323233;
  border: none;
  margin-bottom: 24px;

  :deep(.van-button__text) {
    font-size: 16px;
    font-weight: 500;
  }
}

// 手动安装区域
.manual-section {
  margin-bottom: 24px;
}

.manual-code {
  background: #f7f8fa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.code-header {
  margin-bottom: 16px;
}

.code-label {
  font-size: 16px;
  color: #969799;
  font-weight: 500;
}

.code-container {
  border-radius: 8px;
  display: flex;
  align-items: flex-start;
  gap: 6px;
}

.code-value {
  flex: 1;
  font-size: 14px;
  color: #323233;
  line-height: 1.5;
  word-break: break-all;
  font-family:
    'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.copy-btn {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  border-radius: 8px;
  background: #f7f8fa;
  border: none;
  color: #646566;
  font-size: 20px;

  &:active {
    background: #ebedf0;
  }
}

.code-description {
  font-size: 14px;
  color: #969799;
  line-height: 1.5;
}

// 步骤区域
.steps-section {
  background: white;
  border-radius: 12px;
  padding: 24px 20px;
  margin-bottom: 40px;
}

.step-item {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 12px;
}

.step-instructions {
  margin: 0;
  color: #646566;
}

.instruction-item {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 12px;
  padding-left: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
