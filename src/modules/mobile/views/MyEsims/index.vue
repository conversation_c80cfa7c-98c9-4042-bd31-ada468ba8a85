<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { Icon } from '@/components/Icon'
import PageHeader from '@mobile/components/HeaderTitle.vue'
import EsimEmpty from '@mobile/components/EsimEmpty.vue'
import Copyright from '@mobile/components/Copyright.vue'
import Footer from '@mobile/components/Footer.vue'
import DownloadApp from '@mobile/views/Home/DownloadApp.vue'

const { t } = useI18n()
const router = useRouter()

// eSIM 数据
const esimPlans = ref([
  {
    id: 1,
    title: '5GB 3-Day for United States',
    planType: 'Data Only',
    coverage: 'United States, Canada, Mexico',
    expiry: '23 May 2025 | 02:54 (GMT)',
    status: 'Active',
  },
  {
    id: 2,
    title: '10GB 7-Day for Europe',
    planType: 'Data Only',
    coverage: 'Europe (30+ countries)',
    expiry: '15 Jun 2025 | 14:30 (GMT)',
    status: 'Active',
  },
  {
    id: 3,
    title: '3GB 1-Day for Japan',
    planType: 'Data Only',
    coverage: 'Japan',
    expiry: '10 Apr 2025 | 09:15 (GMT)',
    status: 'Expired',
  },
])

// 计算属性：是否有数据
const hasPlans = computed(() => esimPlans.value.length > 0)

// 处理查看详情
const handleDetails = (planId: number) => {
  router.push({ name: 'esim-detail', params: { id: planId } })
}

// 处理再次购买
const handleBuyAgain = () => {
  showToast(t('myEsims.buyingAgain'))
  router.push('/shop-plans')
}

// 处理购买套餐
const handleShopPlans = () => {
  router.push('/shop-plans')
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'Active':
      return '#00C851'
    case 'Expired':
      return '#FF4757'
    default:
      return '#6B7280'
  }
}
</script>

<template>
  <div class="my-esims-page">
    <!-- 页面标题 -->
    <PageHeader :title="t('myEsims.title')" />

    <!-- eSIM 列表 -->
    <div v-if="hasPlans" class="esim-list">
      <van-cell-group v-for="plan in esimPlans" :key="plan.id" inset class="esim-card">
        <!-- eSIM 卡片头部 -->
        <van-cell class="esim-header">
          <template #title>
            <div><Icon icon="svg-icon:eSIM" :size="40" /></div>
            <div class="esim-title">{{ plan.title }}</div>
          </template>
        </van-cell>
        <!-- eSIM 详情 -->
        <van-cell :title="t('myEsims.planType')" :value="plan.planType" class="esim-detail" />
        <van-cell :title="t('myEsims.coverage')" :value="plan.coverage" class="esim-detail" />
        <van-cell :title="t('myEsims.expiry')" :value="plan.expiry" class="esim-detail" />

        <!-- 操作按钮 -->
        <van-cell class="esim-actions">
          <template #default>
            <div class="action-buttons">
              <van-button plain type="primary" size="small" round @click="handleBuyAgain">
                {{ t('myEsims.buyAgain') }}
              </van-button>
              <van-button type="primary" size="small" round @click="handleDetails(plan.id)">
                {{ t('myEsims.details') }}
              </van-button>
            </div>
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 空状态 -->
    <div v-else class="esim-empty">
      <EsimEmpty>
        <h2 class="empty-title">{{ t('myEsims.empty.title') }}</h2>
        <p class="empty-description">{{ t('myEsims.empty.description') }}</p>
        <van-button type="primary" round size="large" class="shop-btn" @click="handleShopPlans">
          {{ t('myEsims.empty.shopPlans') }}
        </van-button>
      </EsimEmpty>
    </div>
    <DownloadApp />
    <Footer />
    <Copyright />
  </div>
</template>

<style lang="scss" scoped>
.my-esims-page {
  min-height: 100vh;
  width: 100%;
  background-color: var(--vt-c-white-soft);
}

.esim-list {
  padding: 16px;
}

.esim-card {
  margin-bottom: 16px;

  :deep(.van-cell-group) {
    --van-cell-group-inset-radius: 12px;
    overflow: hidden;
  }
}

.esim-header {
  :deep(.van-cell) {
    padding: 16px;
  }

  .esim-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    line-height: 1.4;
    margin-top: 10px;
  }
}

.esim-detail {
  &.van-cell {
    padding: 6px 16px;
    &:after {
      border-bottom: none;
    }
  }
  :deep(.van-cell__title) {
    font-size: 14px;
    color: #000000;
    font-weight: 500;
  }

  :deep(.van-cell__value) {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.5);
    text-align: right;
    flex: none;
    span {
      white-space: nowrap;
    }
  }
}

.esim-actions {
  border-top: 1px solid #e0e0e0;
  margin: 16px 16px 0;
  padding: 16px 0;
  width: calc(100% - 32px);
  :deep(.van-cell__value) {
    flex: 1;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    width: 100%;

    .van-button {
      flex: 1;
      --van-button-default-height: 56px;
      --van-button-small-height: 56px;
      --van-button-default-font-size: 16px;
      --van-button-small-font-size: 16px;
    }
  }
}

.esim-empty {
  padding: 60px 16px;
  text-align: center;

  .empty-title {
    font-size: 24px;
    font-weight: 600;
    color: #000000;
    margin: 24px 0 16px 0;
  }

  .empty-description {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.5);
    margin: 0 0 32px 0;
    line-height: 1.5;
  }

  .shop-btn {
    width: 200px;
    --van-button-large-height: 48px;
    --van-button-large-font-size: 16px;
  }
}

// Vant 组件样式覆盖
:deep(.van-divider) {
  margin: 12px 0;
  border-color: #f0f0f0;
}

:deep(.van-cell) {
  --van-cell-vertical-padding: 12px;
  --van-cell-horizontal-padding: 16px;
}
</style>
