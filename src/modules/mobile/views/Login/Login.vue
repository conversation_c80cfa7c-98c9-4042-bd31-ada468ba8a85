<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuth } from '@/hooks/useAuth'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { Icon } from '@/components/Icon'

const { t } = useI18n()
const router = useRouter()

const {
  email,
  password,
  isLoading,
  showPassword,
  emailValid,
  checkUserExists,
  login,
  resetForm,
  apiLogin,
  showMessage,
  isAuthenticated
} = useAuth()

// 当前步骤
const currentStep = ref<'email' | 'password'>('email')

// 新的登录表单状态
const userCode = ref('')
const loginPassword = ref('')
const showLoginPassword = ref(false)

// 检查是否已登录
if (isAuthenticated.value) {
  router.push('/mobile/')
}

// 真实登录表单验证
const canLogin = computed(() => {
  return userCode.value.length > 0 && loginPassword.value.length > 0
})

// 处理真实 API 登录
const handleApiLogin = async () => {
  if (!canLogin.value) return

  showLoadingToast({
    message: '登录中...',
    forbidClick: true,
  })

  try {
    const result = await apiLogin({
      userCode: userCode.value,
      password: loginPassword.value
    })

    closeToast()

    if (result.success) {
      // 登录成功，跳转到首页
      router.push('/mobile/')
    }
  } catch (error) {
    closeToast()
    console.error('Login error:', error)
  }
}

// 计算属性
const canContinue = computed(() => {
  if (currentStep.value === 'email') {
    return emailValid.value
  } else if (currentStep.value === 'password') {
    return password.value.length > 0
  }
  return false
})

// 处理继续按钮
const handleContinue = async () => {
  if (!canContinue.value) return

  if (currentStep.value === 'email') {
    showLoadingToast({
      message: '检查中...',
      forbidClick: true,
    })

    try {
      const userExists = await checkUserExists(email.value)
      closeToast()

      if (userExists) {
        // 已注册，显示密码输入
        currentStep.value = 'password'
      } else {
        // 未注册，跳转到注册页面
        router.push('/register')
      }
    } catch (error) {
      closeToast()
      showToast('检查失败，请重试')
    }
  } else if (currentStep.value === 'password') {
    showLoadingToast({
      message: '登录中...',
      forbidClick: true,
    })

    const result = await login(email.value, password.value)
    closeToast()

    if (result.success) {
      showToast(result.message)
      router.push('/')
    } else {
      showToast(result.message)
    }
  }
}

// 返回邮箱输入
const handleBackToEmail = () => {
  currentStep.value = 'email'
  password.value = ''
}

// 忘记密码
const handleForgotPassword = () => {
  router.push('/forgot-password')
}

// 社交登录
const handleSocialLogin = async (provider: 'google' | 'apple') => {
  showLoadingToast({
    message: '登录中...',
    forbidClick: true,
  })

  // 这里可以调用 socialLogin 方法
  setTimeout(() => {
    closeToast()
    showToast(`${provider} 登录功能开发中`)
  }, 1000)
}
</script>

<template>
  <div class="auth-container">
    <h1 class="login-title">{{ t('login.title') }}</h1>

    <!-- 真实登录表单 -->
    <div class="real-login-form">
      <van-cell-group inset>
        <!-- 用户名/手机号输入 -->
        <van-field
          v-model="userCode"
          :label="t('login.userCode_label')"
          :placeholder="t('login.userCode_placeholder')"
          clearable
        />

        <!-- 密码输入 -->
        <van-field
          v-model="loginPassword"
          :label="t('login.password_label')"
          :placeholder="t('login.password_placeholder')"
          :type="showLoginPassword ? 'text' : 'password'"
          :right-icon="showLoginPassword ? 'eye-o' : 'closed-eye'"
          clearable
          @click-right-icon="showLoginPassword = !showLoginPassword"
        />
      </van-cell-group>

      <!-- 忘记密码链接 -->
      <div class="forgot-password-link">
        <van-button type="default" size="small" plain @click="handleForgotPassword">
          {{ t('login.forgot_password') }}
        </van-button>
      </div>

      <!-- 登录按钮 -->
      <div class="login-button-wrapper">
        <van-button
          type="primary"
          size="large"
          block
          :loading="isLoading"
          :disabled="!canLogin"
          @click="handleApiLogin"
        >
          {{ t('login.login_button') }}
        </van-button>
      </div>

      <!-- 注册链接 -->
      <div class="register-link">
        <span>{{ t('login.no_account') }}</span>
        <van-button type="default" size="small" plain @click="router.push('/mobile/#/register')">
          {{ t('login.register_link') }}
        </van-button>
      </div>

      <!-- 社交登录 -->
      <div class="social-login">
        <div class="divider">
          <span>{{ t('login.or') }}</span>
        </div>

        <div class="social-buttons">
          <van-button class="social-button" @click="handleSocialLogin('google')">
            <Icon icon="logos:google-icon" />
            {{ t('login.google') }}
          </van-button>

          <van-button class="social-button" @click="handleSocialLogin('apple')">
            <Icon icon="ic:baseline-apple" />
            {{ t('login.apple') }}
          </van-button>
        </div>
      </div>
    </div>

    <!-- 原有的演示登录表单（隐藏） -->
    <div class="demo-login-form" style="display: none;">
      <!-- 邮箱输入 -->
      <van-cell-group inset>
        <van-field
          v-model="email"
          :label="t('login.email_label')"
          :placeholder="t('login.email_placeholder')"
          type="email"
          :readonly="currentStep !== 'email'"
          :right-icon="currentStep !== 'email' ? 'edit' : ''"
          @click-right-icon="handleBackToEmail"
        />
        <template v-if="currentStep === 'password'">
          <van-field
            v-model="password"
            :type="showPassword ? 'text' : 'password'"
            :placeholder="t('login.password_placeholder')"
            :right-icon="showPassword ? 'eye-o' : 'closed-eye'"
            @click-right-icon="showPassword = !showPassword"
          >
            <template #label>
              <span>{{ t('login.password_label') }}</span>
              <div class="forgot-password-link">
                <router-link to="/forgot-password">{{ t('login.forgot_password') }}</router-link>
              </div>
            </template>
          </van-field>
        </template>
      </van-cell-group>
      <!-- 继续/登录按钮 -->
      <div class="auth-button-group">
        <van-button
          type="primary"
          size="large"
          block
          round
          class="auth-primary-btn"
          :loading="isLoading"
          :disabled="!canContinue"
          @click="handleContinue"
        >
          {{ currentStep === 'email' ? t('login.continue') : t('login.login') }}
        </van-button>
      </div>

      <!-- 协议文本 -->
      <div class="auth-agreement-text">
        {{ t('login.agreement_text') }}
        <span class="link">{{ t('login.user_agreement') }}</span>
        {{ t('login.and') }}
        <span class="link">{{ t('login.privacy_policy') }}</span>
      </div>

        <!-- 分割线 -->
        <van-divider>{{ t('login.or') }}</van-divider>

        <!-- 社交登录 -->
        <div class="auth-social-login">
          <van-button size="large" block round @click="handleSocialLogin('google')">
            <template #icon>
              <Icon icon="svg-icon:google"></Icon>
            </template>
            {{ t('login.continue_with_google') }}
          </van-button>
          <van-button size="large" block round @click="handleSocialLogin('apple')">
            <template #icon>
              <Icon icon="svg-icon:apple"></Icon>
            </template>
            {{ t('login.continue_with_apple') }}
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use '@mobile/styles/auth-common.scss';

// 登录页面特有样式
.password-group {
  margin-top: 32px;
}

.forgot-password-link {
  text-align: right;

  a {
    color: #000000;
    text-decoration: underline;
    font-size: 12px;
  }
}

// 所有样式已移至公共样式文件 auth-common.scss
</style>
