<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuth } from '@/hooks/useAuth'
import { showToast, showLoadingToast, closeToast } from 'vant'

const { t } = useI18n()
const router = useRouter()

const {
  email,
  password,
  retypePassword,
  verificationCode,
  isLoading,
  showPassword,
  showRetypePassword,
  codeSent,
  countdown,
  emailValid,
  passwordValid,
  passwordsMatch,
  codeValid,
  passwordError,
  sendVerificationCode,
  resetPassword,
  resetForm,
} = useAuth()

// 计算属性
const canSendCode = computed(() => {
  return emailValid.value && countdown.value === 0
})

const canSave = computed(() => {
  return emailValid.value && codeValid.value && passwordValid.value && passwordsMatch.value
})

// 发送验证码
const handleSendCode = async () => {
  if (!canSendCode.value) return

  showLoadingToast({
    message: t('forgot_password.sending'),
    forbidClick: true,
  })

  const result = await sendVerificationCode(email.value)
  closeToast()

  if (result.success) {
    showToast(result.message || t('forgot_password.code_sent_success'))
  } else {
    showToast(result.message || t('forgot_password.code_sent_failed'))
  }
}

// 保存新密码
const handleSave = async () => {
  if (!canSave.value) return

  showLoadingToast({
    message: t('forgot_password.resetting'),
    forbidClick: true,
  })

  const result = await resetPassword(email.value, verificationCode.value, password.value)
  closeToast()

  if (result.success) {
    showToast(result.message || t('forgot_password.reset_success'))
    resetForm()
    router.push('/login')
  } else {
    showToast(result.message || t('forgot_password.reset_failed'))
  }
}

// 返回登录
const goToLogin = () => {
  router.push('/login')
}
</script>

<template>
  <div class="forgot-password-container">
    <div class="forgot-password-content">
      <div class="forgot-password-title-section">
        <h2 class="auth-title">{{ t('forgot_password.reset_title') }}</h2>
        <p class="auth-subtitle">{{ t('forgot_password.subtitle') }}</p>
      </div>

      <div class="auth-form">
        <!-- 邮箱输入 -->
        <van-cell-group inset>
          <van-field
            v-model="email"
            :label="t('forgot_password.email_label')"
            :placeholder="t('forgot_password.email_placeholder')"
            type="email"
            :readonly="codeSent"
            :error="!!(email && !emailValid)"
            :error-message="t('register.email_error')"
          />
        </van-cell-group>

        <!-- 验证码输入 -->
        <van-cell-group inset>
          <van-field
            v-model="verificationCode"
            :label="t('forgot_password.verification_code_label')"
            :placeholder="t('forgot_password.verification_code_placeholder')"
            maxlength="6"
            :error="!!(verificationCode && !codeValid)"
            :error-message="t('forgot_password.code_error')"
          >
            <template #button>
              <van-button
                size="small"
                type="primary"
                :disabled="!canSendCode"
                :loading="isLoading && !codeSent"
                @click="handleSendCode"
              >
                {{ countdown > 0 ? `${countdown}s` : t('forgot_password.send_code') }}
              </van-button>
            </template>
          </van-field>
        </van-cell-group>

        <!-- 新密码输入 -->
        <van-cell-group inset>
          <van-field
            v-model="password"
            :label="t('forgot_password.new_password_label')"
            :type="showPassword ? 'text' : 'password'"
            :placeholder="t('forgot_password.new_password_placeholder')"
            :right-icon="showPassword ? 'eye-o' : 'closed-eye'"
            :error="!!(password && !passwordValid)"
            :error-message="t('register.password_error')"
            @click-right-icon="showPassword = !showPassword"
          />

          <van-field
            v-model="retypePassword"
            :label="t('forgot_password.confirm_new_password_label')"
            :type="showRetypePassword ? 'text' : 'password'"
            :placeholder="t('forgot_password.confirm_new_password_placeholder')"
            :right-icon="showRetypePassword ? 'eye-o' : 'closed-eye'"
            :error="!!(retypePassword && !passwordsMatch)"
            :error-message="t('register.password_mismatch')"
            @click-right-icon="showRetypePassword = !showRetypePassword"
          />
        </van-cell-group>

        <!-- 保存按钮 -->
        <div class="auth-button-group">
          <van-button
            type="primary"
            size="large"
            block
            round
            class="auth-secondary-btn"
            :loading="isLoading && codeSent"
            :disabled="!canSave"
            @click="handleSave"
          >
            {{ t('forgot_password.save_button') }}
          </van-button>
        </div>

        <!-- 密码要求提示 -->
        <div class="auth-requirements">
          {{ t('forgot_password.password_requirements') }}
        </div>
        <!-- 返回登录链接 -->
        <div class="auth-link">
          <span>{{ t('forgot_password.remember_password') }}</span>
          <van-button type="primary" size="mini" plain @click="goToLogin">
            {{ t('forgot_password.back_to_login') }}
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use '@mobile/styles/auth-common.scss';

.forgot-password-container {
  min-height: 100vh;
  background: #f9fafb;
  display: flex;
  flex-direction: column;
}

.forgot-password-content {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

// 所有通用样式已移至公共样式文件 auth-common.scss
</style>
