<script lang="ts" setup>
import type { PopularDestination } from '@/api/Country/types'

defineProps({
  list: Array<PopularDestination>,
  default: () => [],
})

const EVENT_CLICK = 'click-cell'
const emits = defineEmits([EVENT_CLICK])

const handleClick = (item: PopularDestination) => {
  emits(EVENT_CLICK, item)
}
</script>

<template>
  <div class="destinations-grid">
    <div v-for="item in list" :key="item.iso2" class="destination-card" @click="handleClick(item)">
      <div class="destination-image">
        <img v-if="item.iconUrl" :src="item.iconUrl" :alt="item.value" />
        <p v-else class="holder">{{ (item.value || '').substring(0, 1) }}</p>
      </div>
      <div class="destination-name">{{ item.value }}</div>
      <span class="destination-arrow">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9 18L15 12L9 6"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.destinations-grid {
  padding: 24px 0;
}

.destination-card {
  display: flex;
  align-items: center;
  padding: 18px 16px;
  border-radius: 12px;
  background-color: #ffffff;

  &:active {
    background-color: #f2f3f5;
  }

  & + & {
    margin-top: 12px;
  }
}

.destination-image {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #dddddd;
  overflow: hidden;

  img {
    max-width: 100%;
    object-fit: cover;
  }

  .holder {
    text-align: center;
    line-height: 28px;
    text-transform: uppercase;
  }
}

.destination-name {
  flex: 1;
  min-width: 0;
  margin-left: 16px;
  font-size: 16px;
  color: #000000;
}

.destination-arrow {
  flex: none;
  width: 16px;
  text-align: center;
}
</style>
