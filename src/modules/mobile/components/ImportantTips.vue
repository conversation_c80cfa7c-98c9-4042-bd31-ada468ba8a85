<script setup lang="ts">
import { useI18n } from 'vue-i18n'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useI18n()

// 重要提示列表
const importantTips = [
  {
    title: t('importantTips.stableInternet.title'),
    description: t('importantTips.stableInternet.description'),
  },
  {
    title: t('importantTips.doNotInterrupt.title'),
    description: t('importantTips.doNotInterrupt.description'),
  },
  {
    title: t('importantTips.doNotDelete.title'),
    description: t('importantTips.doNotDelete.description'),
  },
  {
    title: t('importantTips.followInstructions.title'),
    description: t('importantTips.followInstructions.description'),
  },
]

// 处理确认
const handleConfirm = () => {
  emit('update:visible', false)
  emit('confirm')
}

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<template>
  <van-popup
    :show="visible"
    position="center"
    round
    closeable
    close-icon="cross"
    class="important-tips-popup"
    @update:show="handleClose"
  >
    <div class="tips-container">
      <!-- 标题 -->
      <div class="tips-header">
        <h3 class="tips-title">{{ t('importantTips.title') }}</h3>
      </div>

      <!-- 提示列表 -->
      <div class="tips-content">
        <div v-for="(tip, index) in importantTips" :key="index" class="tip-item">
          <div class="tip-bullet">•</div>
          <div class="tip-text">
            <div class="tip-title">{{ tip.title }}</div>
            <div class="tip-description">{{ tip.description }}</div>
          </div>
        </div>
      </div>

      <!-- 确认按钮 -->
      <div class="tips-footer">
        <van-button type="primary" block round class="confirm-btn" @click="handleConfirm">
          {{ t('importantTips.gotIt') }}
        </van-button>
      </div>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
.important-tips-popup {
  :deep(.van-popup) {
    max-width: 90vw;
    width: 360px;
    max-height: 85vh;
    border-radius: 16px;
  }

  :deep(.van-popup__close-icon) {
    top: 16px;
    right: 16px;
    font-size: 18px;
    color: #969799;
  }
}

.tips-container {
  padding: 32px 24px 24px;
  max-height: 75vh;
  overflow-y: auto;
}

.tips-header {
  margin-bottom: 24px;
}

.tips-title {
  font-size: 20px;
  font-weight: 600;
  color: #323233;
  margin: 0;
  text-align: left;
}

.tips-content {
  margin-bottom: 32px;
}

.tip-item {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;

  &:last-child {
    margin-bottom: 0;
  }
}

.tip-bullet {
  font-size: 16px;
  color: #323233;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.tip-text {
  flex: 1;
}

.tip-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
  line-height: 1.4;
}

.tip-description {
  font-size: 14px;
  color: #646566;
  line-height: 1.5;
}

.tips-footer {
  padding-top: 8px;
}

.confirm-btn {
  height: 48px;
  background: #323233;
  border: none;
  border-radius: 24px;

  :deep(.van-button__text) {
    font-size: 16px;
    font-weight: 600;
    color: white;
  }

  &:hover {
    background: #404040;
  }

  &:active {
    background: #1f1f1f;
  }
}

// 滚动条样式
.tips-container::-webkit-scrollbar {
  width: 4px;
}

.tips-container::-webkit-scrollbar-track {
  background: #f7f8fa;
  border-radius: 2px;
}

.tips-container::-webkit-scrollbar-thumb {
  background: #c8c9cc;
  border-radius: 2px;
}

.tips-container::-webkit-scrollbar-thumb:hover {
  background: #a6a7ab;
}
</style>
