// Mobile端 Vant 组件按需导入配置
// 只导入项目中实际使用的 Vant 组件

import type { App } from 'vue'

// 按需导入实际使用的 Vant 组件
import {
  // 基础组件
  Button, // van-button - 在登录、注册等页面使用
  Cell, // van-cell - 单元格组件
  CellGroup, // van-cell-group - 表单容器
  Icon, // van-icon - Header组件中的图标
  Popup, // van-popup - 弹出层
  // 表单组件
  Field, // van-field - 输入框组件

  // 展示组件
  Divider, // van-divider - 分割线
  List, // van-list - 列表组件
  DropdownMenu,
  DropdownItem,
  Switch,
  Sticky,

  // 组合式 API 函数
  showToast, // 轻提示
  showLoadingToast, // 加载提示
  closeToast, // 关闭提示
} from 'vant'

// 需要注册的组件列表
const components = [
  Button, // van-button
  Cell, // van-cell
  CellGroup, // van-cell-group
  Icon, // van-icon
  Popup, // van-popup
  Field, // van-field
  Sticky,
  DropdownMenu,
  DropdownItem,
  Switch,
  Divider, // van-divider
  List, // van-list
]

// 注册组件的函数
export function setupVant(app: App) {
  components.forEach((component) => {
    app.component(<string>component.name, component)
  })
}

// 导出组合式 API 函数，方便在组件中使用
export { showToast, showLoadingToast, closeToast }

// 默认导出设置函数
export default setupVant
