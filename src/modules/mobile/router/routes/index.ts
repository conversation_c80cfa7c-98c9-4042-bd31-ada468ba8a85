import LayoutMobile from '@mobile/layout/index.vue'

export default {
  path: '/',
  redirect: {
    name: 'home',
  },
  component: LayoutMobile,
  children: [
    {
      path: '',
      name: 'home',
      meta: { title: 'nav.home' },
      component: () => import('@mobile/views/Home/index.vue'),
    },
    {
      path: 'refer-earn',
      name: 'refer-earn',
      meta: { title: 'refer.title' },
      component: () => import('@mobile/views/ReferEarn/index.vue'),
    },
    {
      path: 'claim',
      name: 'claim',
      component: () => import('@mobile/views/ReferEarn/Claim.vue'),
      meta: { title: 'claim.title' },
    },
    {
      path: 'shop-plans',
      name: 'shop-plans',
      meta: { title: 'nav.shop_plans' },
      component: () => import('@mobile/views/ShopPlans/index.vue'),
    },
    {
      path: 'plan/:pid',
      name: 'plan-detail',
      meta: { title: 'nav.shop_plans' },
      component: () => import('@mobile/views/ShopPlans/PlanDetail.vue'),
    },
    {
      path: 'checkout/:id',
      name: 'checkout',
      component: () => import('@mobile/views/Checkout/index.vue'),
      meta: { title: 'checkout.title' },
    },
    {
      path: 'checkout/promo',
      name: 'checkout-promo',
      component: () => import('@mobile/views/Promo/index.vue'),
      meta: { title: 'promo.title' },
    },
    {
      path: 'payment',
      name: 'payment',
      component: () => import('@mobile/views/Payment/index.vue'),
      meta: { title: 'payment.title' },
    },
    {
      path: 'login',
      name: 'login',
      meta: { title: 'nav.login' },
      component: () => import('@mobile/views/Login/Login.vue'),
    },
    {
      path: 'register',
      name: 'register',
      meta: { title: 'Register' },
      component: () => import('@mobile/views/Register/Register.vue'),
    },
    {
      path: 'forgot-password',
      name: 'forgot-password',
      meta: { title: 'Forgot Password' },
      component: () => import('@mobile/views/ForgotPassword/ForgotPassword.vue'),
    },
    {
      path: 'country-selection',
      name: 'country-selection',
      meta: { title: 'Select Country' },
      component: () => import('@mobile/views/CountrySelection/CountrySelection.vue'),
    },
    {
      path: 'my-esims',
      name: 'my-esims',
      meta: { title: 'myEsims.title' },
      component: () => import('@mobile/views/MyEsims/index.vue'),
    },
    {
      path: 'esim-detail/:id',
      name: 'esim-detail',
      meta: { title: 'esimDetail.title' },
      component: () => import('@mobile/views/MyEsims/Detail.vue'),
    },
    {
      path: 'installation-instruction/:id',
      name: 'installation-instruction',
      meta: { title: 'installation.title' },
      component: () => import('@mobile/views/MyEsims/InstallationInstruction.vue'),
    },

    {
      path: 'account-settings',
      name: 'account-settings',
      meta: { title: 'accountSettings.title' },
      component: () => import('@mobile/views/Setting/index.vue'),
    },
    {
      path: 'orders',
      name: 'orders',
      meta: { title: 'accountSettings.orders' },
      component: () => import('@mobile/views/Setting/OrdersPage.vue'),
    },
    {
      path: 'my-promos',
      name: 'my-promos',
      meta: { title: 'accountSettings.myPromos' },
      component: () => import('@mobile/views/Setting/MyPromosPage.vue'),
    },
    {
      path: 'points-rewards',
      name: 'points-rewards',
      meta: { title: 'accountSettings.pointsRewards' },
      component: () => import('@mobile/views/Setting/PointsRewardsPage.vue'),
    },
    {
      path: 'edit-password',
      name: 'edit-password',
      meta: { title: 'editPassword.title' },
      component: () => import('@mobile/views/Setting/EditPassword.vue'),
    },
    {
      path: 'edit-email',
      name: 'edit-email',
      meta: { title: 'editEmail.title' },
      component: () => import('@mobile/views/Setting/EditEmail.vue'),
    },
  ],
}
