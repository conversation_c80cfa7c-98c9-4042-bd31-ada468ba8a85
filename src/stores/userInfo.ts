// 用户信息 Store
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { queryUserInfo, userLogout } from '@/api'
import { getToken, getTokenState, getUserInfo, setUserInfo, clearAuth } from '@/utils/token'
import type { QueryUserInfoResponse } from '@/api/User/types'
import { LangType } from '@/api/enums'

export const useUserInfoStore = defineStore('userInfo', () => {
  // 状态
  const userInfo = ref<QueryUserInfoResponse | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const isLoggedIn = computed(() => !!getTokenState().value)

  const displayName = computed(() => {
    if (!userInfo.value) return ''

    // 优先显示昵称
    if (userInfo.value.nickname) {
      return userInfo.value.nickname
    }

    // 其次显示姓名
    if (userInfo.value.firstname || userInfo.value.lastname) {
      return `${userInfo.value.firstname || ''} ${userInfo.value.lastname || ''}`.trim()
    }

    // 最后显示用户编码或邮箱
    return userInfo.value.userCode || userInfo.value.email || 'User'
  })

  const userEmail = computed(() => userInfo.value?.email || '')
  const userPhone = computed(() => userInfo.value?.phone || '')
  const userBalance = computed(() => userInfo.value?.amount || 0)
  const currencyType = computed(() => userInfo.value?.currencyType || 'USD')

  // 方法

  /**
   * 获取用户信息
   */
  const fetchUserInfo = async () => {
    const token = getToken()
    const localUserInfo = getUserInfo()

    if (!token || !localUserInfo?.userId) {
      error.value = 'No valid token or user ID'
      return false
    }

    loading.value = true
    error.value = null

    try {
      const response = await queryUserInfo({
        loginCustomerId: localUserInfo.userId,
        needPkgFirstFlag: false,
        queryAmount: 1, // 查询余额
        langType: LangType.EN_US,
      })

      if (response?.data) {
        userInfo.value = response.data

        // 更新本地存储的用户信息
        setUserInfo({
          ...localUserInfo,
          userCode: response.data.userCode,
          isBindPhone: response.data.isBindPhone || false,
        })

        return true
      } else {
        error.value = 'Failed to fetch user info'
        return false
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch user info'
      console.error('获取用户信息失败:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * 退出登录
   */
  const logout = async () => {
    const token = getToken()
    const localUserInfo = getUserInfo()

    if (!token || !localUserInfo?.userId) {
      // 即使没有 token，也要清除本地数据
      clearUserInfo()
      return true
    }

    loading.value = true
    error.value = null
    try {
      // 调用退出登录 API
      await userLogout({
        loginCustomerId: localUserInfo.userId,
        accessToken: token,
      })

      // 清除用户信息
      clearUserInfo()
      return true
    } catch (err: any) {
      error.value = err.message || 'Logout failed'
      console.error('退出登录失败:', err)

      // 即使 API 调用失败，也要清除本地数据
      clearUserInfo()
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * 清除用户信息
   */
  const clearUserInfo = () => {
    userInfo.value = null
    error.value = null
    clearAuth()
  }

  /**
   * 更新用户信息
   */
  const updateUserInfo = (newUserInfo: Partial<QueryUserInfoResponse>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...newUserInfo }
    }
  }

  /**
   * 初始化用户信息（从本地存储恢复）
   */
  const initUserInfo = async () => {
    const token = getToken()
    if (token) {
      await fetchUserInfo()
    }
  }

  return {
    // 状态
    userInfo,
    loading,
    error,

    // 计算属性
    isLoggedIn,
    displayName,
    userEmail,
    userPhone,
    userBalance,
    currencyType,

    // 方法
    fetchUserInfo,
    logout,
    clearUserInfo,
    updateUserInfo,
    initUserInfo,
  }
})
