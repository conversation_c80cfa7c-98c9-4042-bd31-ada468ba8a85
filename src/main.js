import { createApp } from 'vue'
import * as Sentry from "@sentry/vue";
import App from "./App.vue";
const app = createApp(App)
Sentry.init({
    app,
    dsn: "http://940a381698be51285046e0320952c7d8@localhost:9000/2",
    // Setting this option to true will send default PII data to Sentry.
    // For example, automatic IP address collection on events
    sendDefaultPii: true,
    release: 'test-web@1.0.0',
});
app.mount("#app");
