// 登录注册相关API实现
import { postJSON } from '@/utils/fetch'
import type {
  RegisterUserRequest,
  RegisterUserResponse,
  QuickUserLoginRequest,
  QuickUserLoginResponse,
  GetVerificationCodeBySMSRequest,
  GetVerificationCodeBySMSResponse,
} from './types'
import type { BaseResponse } from '../types'

// API基础路径
const API_BASE_PATH = '/bss/app'

// ==================== 用户注册 ====================

/**
 * 用户注册
 * @param params 注册参数
 * @returns 注册结果
 */
export const registerUser = async (
  params: RegisterUserRequest,
): Promise<BaseResponse<RegisterUserResponse> | undefined> => {
  return await postJSON<BaseResponse<RegisterUserResponse>>({
    url: `${API_BASE_PATH}/noauth/RegisterUser`,
    data: params,
  })
}

// ==================== 用户登录 ====================

/**
 * 快速用户登录
 * @param params 登录参数
 * @returns 登录结果
 */
export const quickUserLogin = async (
  params: QuickUserLoginRequest,
): Promise<QuickUserLoginResponse | undefined> => {
  return await postJSON<QuickUserLoginResponse>({
    url: `${API_BASE_PATH}/noauth/QuickUserLogin`,
    data: params,
  })
}

// ==================== 获取短信验证码 ====================

/**
 * 获取短信验证码
 * @param params 获取验证码参数
 * @returns 验证码发送结果
 */
export const getVerificationCodeBySMS = async (
  params: GetVerificationCodeBySMSRequest,
): Promise<BaseResponse<GetVerificationCodeBySMSResponse> | undefined> => {
  return await postJSON<BaseResponse<GetVerificationCodeBySMSResponse>>({
    url: `${API_BASE_PATH}/GetVerificationCodeBySMS`,
    data: params,
  })
}

// ==================== 导出所有API ====================

export const loginRegistrationApi = {
  registerUser,
  quickUserLogin,
  getVerificationCodeBySMS,
}

// 默认导出
export default loginRegistrationApi
