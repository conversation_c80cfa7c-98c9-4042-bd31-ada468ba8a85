// 登录注册相关API类型定义
import { LangType, RegisterType, ChannelType } from '@/api'
import type { BaseRequest, BaseResponse } from '../types'
// ==================== 用户注册 ====================

export interface RegisterUserRequest extends BaseRequest {
  /** 语言类型 */
  langType: LangType
  /** enterprise编码 - 企业注册流程必传 */
  enterpriseCode?: string
  /** 注册类型 */
  registerType?: RegisterType
  /** 短信验证码 - registerType=PHONE必填 */
  msgCode?: string
  /** 用户编码 */
  userCode?: string
  /** 注册来源 */
  channelType?: ChannelType
  /** 国家编码 */
  countryCode?: string
  /** 密码 - MD5加密一次传输 */
  password?: string
  /** 注册国家 */
  registerCountry: string
  /** 机构编码 */
  orgCode?: string
  /** 图片验证码ID */
  randomId?: string
  /** 图片验证码 */
  randomCode?: string
  /** 发送激活邮件 - 0默认发送激活邮件, 1:不发送激活邮件 */
  sendActiveEmail?: string
}

export interface RegisterUserResponse {
  /** 客户ID */
  customerId: string
}

// ==================== 用户登录 ====================

export interface QuickUserLoginRequest {
  /** 服务的ID - 32位的字符串 */
  clientId: string
  /** 服务的密钥 - 32位的字符串 */
  clientSecret: string
  /** 用户编码 */
  userCode: string
  /** enterprise编码 */
  enterpriseCode: string
  /** 登录密码 - MD5加密一次传输 */
  password: string
  /** 注册来源 */
  channelType: ChannelType
  /** 图片验证码ID */
  randomId: string
  /** 图片验证码 */
  randomCode: string
  /** 客户属性编码集合 */
  custAttrList: string[]
  loginType: string
  /** 允许登录的mvnoCode列表 */
  checkMvnoCodeList: string[]
}

export interface QuickUserLoginResponse {
  /** 用户ID */
  userId: string
  /** Token信息 */
  accessToken: string
  /** 是否绑定手机号标识 */
  isBindPhone: boolean
  /** 客户属性值集合 */
  custAttrValueList: string[]
}

// ==================== 获取短信验证码 ====================

export interface GetVerificationCodeBySMSRequest extends BaseRequest {
  /** 语言类型 */
  langType: LangType
  /** 手机号码 */
  phone?: string
  /** 国家码 */
  nationNum?: string
  /** 获取短信业务类型 - 0:通用业务;1:注册;2:找回密码(换机),3:短信验证码注册登录一体化 */
  businessType: string
  /** 模板编码 - 取值:sms_verification_code_template */
  msgTemplateCode?: string
  /** enterprise编码 - 企业注册流程必传 */
  enterpriseCode?: string
  /** mvno编码 - MVNO注册流程必传 */
  mvnoCode?: string
  /** 机构编码 - 机构注册流程必传 */
  orgCode?: string
}

export interface GetVerificationCodeBySMSResponse {
  /** 有效时长 */
  expireTime: number
}

// ==================== API函数类型定义 ====================

export type RegisterUserAPI = (
  params: RegisterUserRequest,
) => Promise<BaseResponse<RegisterUserResponse>>
export type QuickUserLoginAPI = (params: QuickUserLoginRequest) => Promise<QuickUserLoginResponse>
export type GetVerificationCodeBySMSAPI = (
  params: GetVerificationCodeBySMSRequest,
) => Promise<BaseResponse<GetVerificationCodeBySMSResponse>>
