// Points API 类型定义

export interface PointsTransaction {
  id: number
  type: 'earn' | 'spend'
  amount: number
  description: string
  date: string
  status: 'completed' | 'pending' | 'cancelled'
}

export interface PointsBalance {
  total: number
  available: number
  pending: number
}

export interface PointsHistory {
  transactions: PointsTransaction[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface PointsReward {
  id: number
  name: string
  description: string
  pointsCost: number
  category: string
  image?: string
  available: boolean
}

export interface RedeemPointsRequest {
  rewardId: number
  points: number
}

export interface RedeemPointsResponse {
  success: boolean
  message: string
  transactionId?: number
  newBalance: number
}
