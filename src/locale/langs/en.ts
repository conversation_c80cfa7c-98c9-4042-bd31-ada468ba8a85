export default {
  common: {
    search: 'Search',
    confirm: 'Confirm',
    cancel: 'Cancel',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    failed: 'Failed',
    retry: 'Retry',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    prev: 'Previous',
    finish: 'Finish',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    add: 'Add',
    submit: 'Submit',
    reset: 'Reset',
    pagination: {
      of: 'of',
      firstPage: 'First page',
      prevPage: 'Previous page',
      nextPage: 'Next page',
      lastPage: 'Last page',
    },
  },
  app: {
    title: 'esim store',
  },
  search: {
    placeholder: 'Search for your destination worldwide',
  },
  nav: {
    home: 'Home',
    mobile: 'Mobile',
    shop_plans: 'Shop Plans',
    my_esims: 'My eSIMs',
    about_us: 'About us',
    login: 'Login',
    logout: 'Logout',
    register: 'Sign Up',
    orders: 'Orders',
    my_promos: 'My Promos',
    points_rewards: 'Points & Rewards',
    account_settings: 'Account Settings',
    please_login: 'Please log in to access more features',
  },
  common: {
    balance: 'Balance',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
  },
  login: {
    title: 'Log In',
    email_label: 'Email',
    email_placeholder: 'Please enter',
    userCode_label: 'Phone Number / Email',
    userCode_placeholder: 'Enter your phone number or email',
    password_label: 'Password',
    password_placeholder: 'Enter your password',
    login_button: 'Log In',
    forgot_password: 'Forgot Password?',
    no_account: "Don't have an account?",
    register_link: 'Sign Up',
    continue: 'Continue',
    agreement_text: 'By logging in or creating an account, you agree to',
    user_agreement: 'User Agreement',
    and: 'and',
    privacy_policy: 'Privacy Policy',
    or: 'OR',
    continue_with_google: 'Continue with Google',
    continue_with_apple: 'Continue with Apple',
    continue_with_mobile: 'Continue with Mobile phone',
    google: 'Google',
    apple: 'Apple',
    login: 'Log In',
  },
  register: {
    title: 'Create your account',
    phone_label: 'Phone Number',
    phone_placeholder: 'Enter your phone number',
    verification_code_label: 'Verification Code',
    verification_code_placeholder: 'Enter 6-digit code',
    send_code: 'Send Code',
    password_label: 'Password',
    password_placeholder: 'Please enter password',
    confirm_password_label: 'Retype Password',
    confirm_password_placeholder: 'Please enter password again',
    register_button: 'Register',
    registering: 'Registering...',
    email_error: 'Please enter a valid email address',
    password_error:
      'Password must be 6-20 characters long and include at least two of the following: letters, numbers, or symbols. It also cannot be one you’ve used before.',
    password_mismatch: 'Passwords do not match',
    register_success: 'Registration successful!',
    register_failed: 'Registration failed, please try again',
    user_exists: 'User already exists, please login directly',
    google_register_developing: 'Google registration feature is under development',
    apple_register_developing: 'Apple registration feature is under development',
    go_to_login: 'Already have an account? Sign in',
    select_residence: 'Select your place of residence',
    country_region: 'Country / region',
    sign_up: 'Sign up',
    select_country_placeholder: 'Please select country/region',
  },
  forgot_password: {
    title: 'Forgot Password',
    back: 'Back',
    reset_title: 'Reset Password',
    subtitle: 'We will send a verification code to your email, please enter the 6-digit code',
    email_label: 'Email',
    email_placeholder: '<EMAIL>',
    verification_code_label: 'Verification Code',
    verification_code_placeholder: 'Please enter 6-digit code',
    new_password_label: 'New Password',
    new_password_placeholder: 'Please enter new password',
    confirm_new_password_label: 'Confirm Password',
    confirm_new_password_placeholder: 'Please enter new password again',
    send_code: 'Send Code',
    sending: 'Sending...',
    resetting: 'Resetting...',
    save_button: 'Save',
    code_error: 'Please enter 6-digit verification code',
    password_requirements:
      "Password must be 6-20 characters long and include at least two of the following: letters, numbers, or symbols. It also cannot be one you've used before.",
    remember_password: 'Remember your password?',
    back_to_login: 'Back to Login',
    code_sent_success: 'Verification code sent',
    code_sent_failed: 'Failed to send verification code',
    reset_success: 'Password reset successful',
    reset_failed: 'Password reset failed',
  },
  home: {
    title: 'Connect to Ideal Network at',
    title_highlight: 'Affordable Rates',
    adventure: 'Summer Sale: use promo code for 10% off!',
    adventure_btn: 'SUM10',
    share_friend: 'Bring a friend and enjoy USD 5 off!',
    popular: {
      title: 'POPULAR DESTINATIONS',
      local: 'Local eSIMs',
      regional: 'Regional eSIMs',
    },
    download_app: {
      title: 'DOWNLOAD THE GLOCALME APP',
      description:
        'Download the GlocalMe app to purchase, manage, and top up your plans anytime, anywhere!',
      app_store: 'Download on the App Store',
      google_play: 'Get it on Google Play',
    },
    refer_friend: {
      title: 'BRING A FRIEND AND',
      highlight: 'ENJOY $5 OFF',
      button: 'Refer a Friend',
    },
    user_reviews: {
      title: 'HEAR FROM GLOCALME USERS!',
    },
    how_it_works: {
      title: 'HOW DOES GLOCALME WORK?',
      steps: {
        step1: {
          step: 'STEP 01',
          title: 'Login',
          description: 'Install the GlocalMe app and create a new account',
        },
        step2: {
          step: 'STEP 02',
          title: 'Shop',
          description:
            'Navigate to Mall > eSIM & Travel SIM > eSIM Standard. Choose your destination and plan',
        },
        step3: {
          step: 'STEP 03',
          title: 'Activation',
          description:
            'Access Order Details > eSIM installation instructions for your QR code or activation code. Ensure network connectivity and follow the steps to complete eSIM activation.',
        },
        step4: {
          step: 'STEP 04',
          title: 'Access',
          description:
            'Enable the "Data Roaming" toggle for your new eSIM plan to connect to the network',
        },
      },
    },
    exclusive: {
      tag: 'CEO EXCLUSIVE',
      title: '100GB 365-DAY',
      subtitle: 'PACKAGE FOR GLOBAL',
      button: 'Learn More',
    },
  },
  footer: {
    start_travel: 'Start your travel with GlocalMe!',
    stay_connected: 'Stay Connected',
    shop_plans: 'Shop Plans',
    refer_friend: 'Refer a Friend',
    my_promos: 'My Promos',
    about_us: 'About us',
    about_glocalme: 'About GlocalMe',
    terms: 'Terms & Conditions',
    privacy_policy: 'Privacy Policy',
    contact_us: 'Contact Us',
    follow_us: 'Follow us',
    scan_download_app: 'Scan and download the app',
  },
  myEsims: {
    title: 'My eSIMs',
    planType: 'Plan type',
    coverage: 'Coverage',
    expiry: 'Expiry',
    details: 'Details',
    buyAgain: 'Buy again',
    viewingDetails: 'Viewing details...',
    buyingAgain: 'Redirecting to shop...',
    empty: {
      title: 'No eSIM Plans... yet!',
      description: 'Purchase a plan for it to appear here.',
      shopPlans: 'Shop Plans',
    },
    downloadApp: {
      title: 'DOWNLOAD THE GLOCALME APP',
      description:
        'Download the GlocalMe app to purchase, manage, and top up your plans anytime, anywhere!',
    },
  },
  esimDetail: {
    title: 'eSIM Details',
    data: 'Data',
    validity: 'Validity',
    installation: {
      title: 'eSIM Installation',
      button: 'Install',
      instruction: 'Installation Instruction',
      starting: 'Starting installation...',
    },
    description: 'Description',
  },
  installation: {
    title: 'Installation Instruction',
    warning: {
      label: 'WARNING!',
      message:
        'Most eSIMs can only be installed once. If you remove the eSIM from your device, you cannot install it again.',
    },
    qrDescription:
      'Scan the QR code by printing out or displaying the code on another device to install your eSIM. Make sure your device has a stable internet connection before installing.',
    saveAsImage: 'Save as image',
    activationCode: 'Activation Code',
    manualCode: {
      label: 'SM-DP+Address & Activation Code',
      description:
        'Copy this information and enter details manually to install your eSIM. *Make sure your device has a stable internet connection before installing.',
    },
    copySuccess: 'Activation code copied to clipboard',
    android: {
      step1: {
        title: 'Step 1/2 Install eSIM',
        instruction1:
          '1. Go to Settings, tap "Connections", then tap "SIM manager" on your device.',
        instruction2: '2. Tap "Add eSIM", then tap "Scan QR code".',
        instruction3: '3. Scan the QR code, then tap "Confirm".',
      },
      step2: {
        title: 'Step 2/2 Access Data',
        instruction1:
          '1. Go to "SIM manager", then turn on your eSIM by enabling the toggle, then tap "OK" on your device.',
        instruction2: '2. Select your eSIM for mobile data.',
        instruction3:
          '3. Go to "Connections" > "Mobile networks", then enable the "Data roaming" toggle.',
      },
    },
    ios: {
      step1: {
        title: 'Step 1/2 Install eSIM',
        instruction1:
          '1. Go to Settings > Cellular > "Add eSIM" or "Set up Cellular" > "Use QR Code" on your device.',
        instruction2: '2. Scan the QR code, then tap "Continue" twice and wait for a while.',
        instruction3:
          '3. Your eSIM will connect to the network, this may take a few minutes, then tap "Done".',
        instruction4: '4. Choose a label for your new eSIM plan.',
        instruction5: '5. Choose "Primary" for your default line, then tap "Continue".',
        instruction6:
          '6. Choose the "Primary" you want to use with iMessage and FaceTime for your Apple ID, then tap "Continue".',
        instruction7: '7. Choose your new eSIM plan for Cellular Data, then tap "Continue".',
      },
      step2: {
        title: 'Step 2/2 Access Data',
        instruction1:
          '1. Go to Settings > Cellular > Cellular Data, then select the recently downloaded eSIM on your device.',
        instruction2:
          '2. Enable the "Turn On This Line" and "Data Roaming" toggle respectively for your new eSIM plan on SIMs list.',
      },
    },
    saveImageSuccess: 'QR code saved to gallery',
  },
  importantTips: {
    title: 'Important Tips',
    message:
      'Please ensure your device has a stable internet connection and follow all installation steps carefully. Most eSIMs can only be installed once.',
    stableInternet: {
      title: 'Stable Internet Required',
      description:
        'Ensure your device has a stable connection to prevent eSIM installation errors.',
    },
    doNotInterrupt: {
      title: 'Do Not Interrupt',
      description: 'Avoid disrupting the eSIM installation to prevent errors.',
    },
    doNotDelete: {
      title: 'Do Not Delete',
      description:
        "Most eSIMs can be installed only once. Removing it means it can't be reinstalled.",
    },
    followInstructions: {
      title: 'Follow Instructions',
      description: 'Carefully follow all steps for successful eSIM activation.',
    },
    gotIt: 'Got It',
  },
  accountSettings: {
    title: 'Account Settings',
    fullName: 'Full Name',
    emailAddress: 'Email address',
    password: 'Password',
    placeOfResidence: 'Place of residence',
    marketingEmails: 'I agree to receive marketing emails',
    edit: 'Edit',
    orders: 'Orders',
    myPromos: 'My Promos',
    pointsRewards: 'Points & Rewards',
    editFullName: {
      title: 'Edit Full Name',
      placeholder: 'Enter your full name',
      save: 'Save',
      cancel: 'Cancel',
    },
    editEmail: {
      title: 'Edit Email Address',
      placeholder: 'Enter your email address',
      save: 'Save',
      cancel: 'Cancel',
    },
    editPassword: {
      title: 'Edit Password',
      currentPassword: 'Current Password',
      newPassword: 'New Password',
      confirmPassword: 'Confirm New Password',
      save: 'Save',
      cancel: 'Cancel',
    },
    nameUpdated: 'Name updated successfully',
  },
  orders: {
    title: 'Orders',
    orderName: 'Order Name',
    orderDate: 'Order Date',
    amount: 'Amount',
    status: 'Status',
    noOrders: 'No orders found',
    noOrdersDescription: "You haven't placed any orders yet.",
    pagination: {
      of: 'of',
    },
  },
  myPromos: {
    title: 'My Promos',
    totalPromos: 'Total Promos',
    coupon: 'Coupon',
    promoCode: 'Promo Code',
    validUntil: 'Valid until',
    noMinimum: 'No-Minimum',
    minimumSpending: 'Minimum spending of',
    redeemTitle: 'Have a promo/referral code?',
    enterCode: 'Enter code',
    claim: 'Claim',
    pagination: {
      of: 'of',
    },
  },
  pointsRewards: {
    title: 'Points & Rewards',
    points: 'Points',
    balance: 'Balance',
    redeemTitle: 'Redeem products with points',
    redeemDescription:
      'Earn points through purchases and redeem points for data, coupons, balance and physical goods exclusively on the GlocalMe App.',
    downloadApp: 'Download GlocalMe App',
    purchaseHistory: 'Purchase history',
    purchaseRewards: 'Purchase rewards',
    pointsEarned: 'points',
    pagination: {
      of: 'of',
    },
  },
  installModal: {
    qrCode: 'QR Code',
    manual: 'Manual',
    warning: 'WARNING:',
    warningText:
      'Most eSIMs can only be installed once. If you remove the eSIM from your device, you cannot install it again.',
    smDpAddress: 'SM-DP+Address',
    activationCode: 'Activation Code',
    copy: 'Copy',
    installNote:
      'Copy this information and enter details manually to install your eSIM. *Make sure your device has a stable internet connection before installing.',
    qrTip:
      'Scan the QR code by printing out or displaying the code on another device to install your eSIM. *Make sure your device has a stable internet connection before installing.',
    saveImage: 'Save as image',
    steps: {
      installTitle: 'Step 1/2 Install eSIM',
      step1:
        'Go to Settings > Cellular/Mobile Data > Add eSIM or Set up Cellular/Mobile Service on your device.',
      step2:
        'Scan the QR code or take a screenshot (available up to iOS 17.4), tap "Open" then tap "Continue" to install the eSIM.',
      step3:
        'If you are unable to scan the QR code, tap "Enter Details Manually" and enter the information provided above.',
      step4: 'Choose "Primary" for your default line, then tap "Continue".',
      step5:
        'Choose the "Primary" you want to use with iMessage and FaceTime for your Apple ID, then tap "Continue".',
      step6: 'Choose your new eSIM plan for cellular/mobile data, then tap "Continue".',
      accessTitle: 'Step 2/2 Access Data',
      access1:
        'Go to "Cellular/Mobile Data", then select the recently downloaded eSIM on your phone. Turn On "Turn On This Line" toggle, then select your new eSIM plan for cellular/mobile data.',
      access2:
        'Tap "Network Selection", disable the "Automatic" toggle, then select the network that appears. If your eSIM has connected to the wrong network.',
      access3: 'Tap "Roaming", then enable "Data Roaming" for your new eSIM plan.',
    },
    stepGuide: 'Go to Step-by-Step Guide',
  },
  copyright: {
    text: `Copyright{'@'}2025 GlocalMe uCloudlink Group Inc.`,
  },
  promo: {
    title: 'My Promo',
    available_promos: 'Available Promos',
    unavailable_promos: 'Unavailable Promos',
    promo_code: 'Promo Code',
    coupon: 'Coupon',
    valid_until: 'Valid until',
    best_savings: 'BEST SAVINGS',
    use_promo: 'Use Promo',
  },
  checkout: {
    title: 'Checkout',
    activate_later: 'Can I activate my plan later?',
    activation_period_text:
      "All plans have a {period} activation period. If you get a plan today and don't activate it until {deadline}, it will be activated automatically.",
    promo: 'Promo',
    my_promo: 'My Promo',
    apply: 'Apply',
    order_summary: 'Order Summary',
    item: 'Item',
    subtotal: 'Subtotal',
    vip_discount: "VIP's 5% OFF",
    total: 'Total',
    place_order: 'Place your order',
  },
  payment: {
    title: 'Payment',
    failed_title: 'Payment Failed',
    error_message: 'Oops, something went wrong.',
    error_description: "We're sorry, an error has occurred. Please try again later.",
    back_to_checkout: 'Back to Checkout',
  },
  order: {
    completed_title: 'Order Completed',
    thank_you: 'Thank you for your order!',
    number: 'Order no.',
    go_to_orders: 'Go to Orders',
    how_to_install: 'How to install an eSIM?',
    install_instruction: 'You can install your eSIM by the instruction.',
    esim_details: 'eSIM Details',
  },
  refer: {
    title: 'Refer & Earn',
    reward_title: 'Give USD 5 & Get USD 5',
    subtitle: 'Share GlocalMe with friends',
    description:
      'Get USD 5 for every friend that signs up and completes a purchase. Your friends get USD 5 off their first purchase. Any cheating behavior will result in the suspension of reward distribution.',
    your_code: 'Your Referral Code',
    copy: 'Copy',
    copy_success: 'Copied successfully',
    copy_failed: 'Copy failed',
    share_now: 'Share Now',
    share_title: 'GlocalMe Referral Program',
    share_text: 'Sign up for GlocalMe using my referral code {code} and get $5 off!',
    share_manually: 'Please share your referral code manually',
    reward_history: 'Reward History',
    invitation_rewards: 'Invitation rewards',
  },
  claim: {
    title: 'Hey, wanna try eSIM?',
    reward_title: 'USD 5 & Credit for you',
    subtitle: 'to stay connected with GlocalMe',
    code_label: 'Promo Code',
    copy: 'Copy',
    copy_success: 'Copied successfully',
    copy_failed: 'Copy failed',
    sign_up: 'Sign up and Claim',
    how_to_use: 'How to use the promo code?',
    step1: '1. Click "Sign up and Claim" to create an account in seconds.',
    step2: '2. Redeem the promo code after signing up.',
    step3: '3. Then purchase an eSIM plan with the discount.',
  },
  editPassword: {
    title: 'Edit Password',
    verificationMessage:
      "Next, we'll send a verification code to your email. Please enter the 6-digit code we sent.",
    verificationCode: 'Verification code',
    sendCode: 'Send code',
    newPassword: 'New Password',
    retypePassword: 'Retype Password',
    passwordRequirements:
      "Password must be 6-20 characters long and include at least two of the following: letters, numbers, or symbols. It also cannot be one you've used before.",
    save: 'Save',
    sendingCode: 'Sending code...',
    codeSent: 'Verification code sent',
    sendCodeFailed: 'Failed to send code',
    saving: 'Saving...',
    saveSuccess: 'Password updated successfully',
    saveFailed: 'Failed to update password',
  },
  editEmail: {
    title: 'Edit Email',
    verificationMessage:
      "Next, we'll send a verification code to your email. Please enter the 6-digit code we sent.",
    verificationCode: 'Verification code',
    sendCode: 'Send code',
    newEmail: 'New Email',
    save: 'Save',
    sendingCode: 'Sending code...',
    codeSent: 'Verification code sent',
    sendCodeFailed: 'Failed to send code',
    saving: 'Saving...',
    saveSuccess: 'Email updated successfully',
    saveFailed: 'Failed to update email',
  },
  editName: {
    title: 'Edit Name',
    placeholder: 'Enter your name',
    cancel: 'Cancel',
    save: 'Save',
    saving: 'Saving...',
    saveSuccess: 'Name updated successfully',
    saveFailed: 'Failed to update name',
    nameRequired: 'Name cannot be empty',
  },
}
