export default {
  common: {
    search: '搜索',
    confirm: '确认',
    cancel: '取消',
    loading: '加载中...',
    error: '出错了',
    success: '成功',
    failed: '失败',
    retry: '重试',
    close: '关闭',
    back: '返回',
    next: '下一步',
    prev: '上一步',
    finish: '完成',
    save: '保存',
    edit: '编辑',
    delete: '删除',
    add: '添加',
    submit: '提交',
    reset: '重置',
    pagination: {
      of: '共',
      firstPage: '首页',
      prevPage: '上一页',
      nextPage: '下一页',
      lastPage: '末页',
    },
  },
  app: {
    title: 'esim 商场',
  },
  search: {
    placeholder: '搜索您的全球目的地',
  },
  nav: {
    home: '首页',
    mobile: '移动端',
    shop_plans: '购买套餐',
    my_esims: '我的eSIM',
    about_us: '关于我们',
    login: '登录',
    logout: '退出登录',
    register: '注册',
    orders: '订单',
    my_promos: '我的优惠',
    points_rewards: '积分奖励',
    account_settings: '账户设置',
    please_login: '请登录以访问更多功能',
  },
  common: {
    balance: '余额',
    loading: '加载中...',
    error: '错误',
    success: '成功',
  },
  login: {
    title: '登录',
    email_label: '邮箱',
    email_placeholder: '请输入',
    userCode_label: '手机号 / 邮箱',
    userCode_placeholder: '请输入手机号或邮箱',
    password_label: '密码',
    password_placeholder: '请输入密码',
    login_button: '登录',
    forgot_password: '忘记密码？',
    no_account: '还没有账户？',
    register_link: '注册',
    continue: '继续',
    agreement_text: '通过登录或创建账户，您同意',
    user_agreement: '用户协议',
    and: '和',
    privacy_policy: '隐私政策',
    or: '或',
    continue_with_google: '使用Google继续',
    continue_with_apple: '使用Apple继续',
    continue_with_mobile: '使用手机号继续',
    google: 'Google',
    apple: 'Apple',
    login: '登录',
  },
  register: {
    title: '创建您的账户',
    phone_label: '手机号',
    phone_placeholder: '请输入手机号',
    verification_code_label: '验证码',
    verification_code_placeholder: '请输入6位验证码',
    send_code: '发送验证码',
    password_label: '密码',
    password_placeholder: '请输入密码',
    confirm_password_label: '确认密码',
    confirm_password_placeholder: '请再次输入密码',
    register_button: '注册',
    registering: '注册中...',
    email_error: '请输入正确的邮箱地址',
    password_error: '密码必须6-20位，包含字母、数字和符号',
    password_mismatch: '两次输入的密码不一致',
    register_success: '注册成功！',
    register_failed: '注册失败，请重试',
    user_exists: '用户已存在，请直接登录',
    google_register_developing: 'Google 注册功能开发中',
    apple_register_developing: 'Apple 注册功能开发中',
    go_to_login: '已有账户？去登录',
    select_residence: '选择您的居住地',
    country_region: '国家/地区',
    sign_up: '注册',
    select_country_placeholder: '请选择国家/地区',
  },
  forgot_password: {
    title: '忘记密码',
    back: '返回',
    reset_title: '重置密码',
    subtitle: '我们将发送验证码到您的邮箱，请输入6位验证码',
    email_label: '邮箱',
    email_placeholder: '<EMAIL>',
    verification_code_label: '验证码',
    verification_code_placeholder: '请输入6位验证码',
    new_password_label: '新密码',
    new_password_placeholder: '请输入新密码',
    confirm_new_password_label: '确认密码',
    confirm_new_password_placeholder: '请再次输入新密码',
    send_code: '发送验证码',
    sending: '发送中...',
    resetting: '重置中...',
    save_button: '保存',
    code_error: '请输入6位数字验证码',
    password_requirements:
      '密码必须6-20位，包含字母、数字和符号中的至少两种。不能是您之前使用过的密码。',
    remember_password: '想起密码了？',
    back_to_login: '返回登录',
    code_sent_success: '验证码已发送',
    code_sent_failed: '验证码发送失败',
    reset_success: '密码重置成功',
    reset_failed: '密码重置失败',
  },
  home: {
    title: '连接理想网络以',
    title_highlight: '实惠的价格',
    adventure: '夏日促销：使用优惠码享受九折优惠！',
    adventure_btn: 'SUM10',
    share_friend: '邀请朋友，享受5美元优惠！',
    popular: {
      title: '热门目的地',
      local: '本地eSIM卡',
      regional: '区域eSIM卡',
    },
    download_app: {
      title: '下载GLOCALME应用',
      description: '下载GlocalMe应用，随时随地购买、管理和充值您的套餐！',
      app_store: '从App Store下载',
      google_play: '从Google Play获取',
    },
    refer_friend: {
      title: '带上朋友一起',
      highlight: '享受5美元优惠',
      button: '推荐朋友',
    },
    user_reviews: {
      title: '来自GlocalMe用户的评价！',
    },
    how_it_works: {
      title: 'GLOCALME如何工作？',
      steps: {
        step1: {
          step: '步骤01',
          title: '登录',
          description: '安装GlocalMe应用并创建新账户',
        },
        step2: {
          step: '步骤02',
          title: '购买',
          description: '导航至商城 > eSIM和旅行SIM > eSIM标准版。选择您的目的地和套餐',
        },
        step3: {
          step: '步骤03',
          title: '激活',
          description:
            '访问订单详情 > eSIM安装说明获取您的二维码或激活码。确保网络连接并按照步骤完成eSIM激活。',
        },
        step4: {
          step: '步骤04',
          title: '访问',
          description: '为您的新eSIM套餐启用"数据漫游"开关以连接网络',
        },
      },
    },
    exclusive: {
      tag: 'CEO专享',
      title: '100GB 365天',
      subtitle: '全球套餐',
      button: '了解更多',
    },
  },
  footer: {
    start_travel: '开始您的GlocalMe旅程！',
    stay_connected: '保持连接',
    shop_plans: '购买套餐',
    refer_friend: '推荐朋友',
    my_promos: '我的优惠',
    about_us: '关于我们',
    about_glocalme: '关于GlocalMe',
    terms: '条款和条件',
    privacy_policy: '隐私政策',
    contact_us: '联系我们',
    follow_us: '关注我们',
    scan_download_app: '扫码下载应用',
  },
  myEsims: {
    title: '我的eSIM',
    planType: '套餐类型',
    coverage: '覆盖范围',
    expiry: '到期时间',
    details: '详情',
    buyAgain: '再次购买',
    viewingDetails: '正在查看详情...',
    buyingAgain: '正在跳转到商店...',
    empty: {
      title: '暂无eSIM套餐！',
      description: '购买套餐后将在此处显示。',
      shopPlans: '购买套餐',
    },
    downloadApp: {
      title: '下载GLOCALME应用',
      description: '下载GlocalMe应用，随时随地购买、管理和充值您的套餐！',
    },
  },
  esimDetail: {
    title: 'eSIM详情',
    data: '数据',
    validity: '有效期',
    installation: {
      title: 'eSIM安装',
      button: '安装',
      instruction: '安装指令',
      starting: '正在开始安装...',
    },
    description: '描述',
  },
  installation: {
    title: '安装指令',
    warning: {
      label: '警告！',
      message: '大多数eSIM只能安装一次。如果您从设备中移除eSIM，将无法再次安装。',
    },
    qrDescription:
      '通过打印或在另一台设备上显示二维码来扫描安装您的eSIM。请确保您的设备在安装前有稳定的网络连接。',
    saveAsImage: '保存为图片',
    activationCode: '激活码',
    manualCode: {
      label: 'SM-DP+地址和激活码',
      description:
        '复制此信息并手动输入详细信息来安装您的eSIM。*请确保您的设备在安装前有稳定的网络连接。',
    },
    copySuccess: '激活码已复制到剪贴板',
    android: {
      step1: {
        title: '步骤 1/2 安装eSIM',
        instruction1: '1. 进入设置，点击"连接"，然后点击"SIM卡管理器"。',
        instruction2: '2. 点击"添加eSIM"，然后点击"扫描二维码"。',
        instruction3: '3. 扫描二维码，然后点击"确认"。',
      },
      step2: {
        title: '步骤 2/2 访问数据',
        instruction1: '1. 进入"SIM卡管理器"，通过启用开关打开您的eSIM，然后点击"确定"。',
        instruction2: '2. 选择您的eSIM作为移动数据。',
        instruction3: '3. 进入"连接" > "移动网络"，然后启用"数据漫游"开关。',
      },
    },
    ios: {
      step1: {
        title: '步骤 1/2 安装eSIM',
        instruction1: '1. 进入设置 > 蜂窝网络 > "添加eSIM"或"设置蜂窝网络" > "使用二维码"。',
        instruction2: '2. 扫描二维码，然后点击"继续"两次并等待一会儿。',
        instruction3: '3. 您的eSIM将连接到网络，这可能需要几分钟，然后点击"完成"。',
        instruction4: '4. 为您的新eSIM套餐选择一个标签。',
        instruction5: '5. 为您的默认线路选择"主要"，然后点击"继续"。',
        instruction6: '6. 选择您想要用于Apple ID的iMessage和FaceTime的"主要"线路，然后点击"继续"。',
        instruction7: '7. 为蜂窝数据选择您的新eSIM套餐，然后点击"继续"。',
      },
      step2: {
        title: '步骤 2/2 访问数据',
        instruction1: '1. 进入设置 > 蜂窝网络 > 蜂窝数据，然后在您的设备上选择最近下载的eSIM。',
        instruction2: '2. 在SIM卡列表中为您的新eSIM套餐分别启用"打开此线路"和"数据漫游"开关。',
      },
    },
    saveImageSuccess: '二维码已保存到相册',
  },
  importantTips: {
    title: '重要提示',
    message: '请确保您的设备有稳定的网络连接，并仔细遵循所有安装步骤。大多数eSIM只能安装一次。',
    stableInternet: {
      title: '需要稳定的网络连接',
      description: '确保您的设备有稳定的连接，以防止eSIM安装错误。',
    },
    doNotInterrupt: {
      title: '请勿中断',
      description: '避免中断eSIM安装过程，以防止错误。',
    },
    doNotDelete: {
      title: '请勿删除',
      description: '大多数eSIM只能安装一次。删除后将无法重新安装。',
    },
    followInstructions: {
      title: '遵循说明',
      description: '仔细遵循所有步骤以成功激活eSIM。',
    },
    gotIt: '知道了',
  },
  accountSettings: {
    title: '账号设置',
    fullName: '全名',
    emailAddress: '邮箱地址',
    password: '密码',
    placeOfResidence: '居住地',
    marketingEmails: '我同意接收营销邮件',
    edit: '编辑',
    orders: '订单',
    myPromos: '我的优惠',
    pointsRewards: '积分与奖励',
    editFullName: {
      title: '编辑全名',
      placeholder: '请输入您的全名',
      save: '保存',
      cancel: '取消',
    },
    editEmail: {
      title: '编辑邮箱地址',
      placeholder: '请输入您的邮箱地址',
      save: '保存',
      cancel: '取消',
    },
    editPassword: {
      title: '编辑密码',
      currentPassword: '当前密码',
      newPassword: '新密码',
      confirmPassword: '确认新密码',
      save: '保存',
      cancel: '取消',
    },
    nameUpdated: '姓名更新成功',
  },
  orders: {
    title: '订单',
    orderName: '订单名称',
    orderDate: '订单日期',
    amount: '金额',
    status: '状态',
    noOrders: '暂无订单',
    noOrdersDescription: '您还没有下过任何订单。',
    pagination: {
      of: '共',
    },
  },
  myPromos: {
    title: '我的优惠',
    totalPromos: '优惠券总数',
    coupon: '优惠券',
    promoCode: '优惠码',
    validUntil: '有效期至',
    noMinimum: '无最低消费',
    minimumSpending: '最低消费',
    redeemTitle: '有优惠/推荐码吗？',
    enterCode: '输入代码',
    claim: '领取',
    pagination: {
      of: '共',
    },
  },
  pointsRewards: {
    title: '积分与奖励',
    points: '积分',
    balance: '余额',
    redeemTitle: '用积分兑换产品',
    redeemDescription:
      '通过购买赚取积分，并在GlocalMe应用中独家兑换积分获得数据、优惠券、余额和实物商品。',
    downloadApp: '下载GlocalMe应用',
    purchaseHistory: '购买历史',
    purchaseRewards: '购买奖励',
    pointsEarned: '积分',
    pagination: {
      of: '共',
    },
  },
  installModal: {
    qrCode: '二维码',
    manual: '手动输入',
    warning: '警告：',
    warningText: '大多数eSIM只能安装一次。如果您从设备中移除eSIM，将无法再次安装。',
    smDpAddress: 'SM-DP+地址',
    activationCode: '激活码',
    copy: '复制',
    installNote:
      '复制此信息并手动输入详细信息以安装您的eSIM。*请确保您的设备在安装前有稳定的网络连接。',
    qrTip:
      '通过打印或在另一台设备上显示二维码来扫描二维码以安装您的eSIM。*请确保您的设备在安装前有稳定的网络连接。',
    saveImage: '保存图片',
    steps: {
      installTitle: '步骤 1/2 安装eSIM',
      step1: '前往设置 > 蜂窝网络/移动数据 > 添加eSIM或在您的设备上设置蜂窝网络/移动服务。',
      step2: '扫描二维码或截屏（iOS 17.4及以上版本可用），点击"打开"然后点击"继续"安装eSIM。',
      step3: '如果无法扫描二维码，请点击"手动输入详细信息"并输入上面提供的信息。',
      step4: '为您的默认线路选择"主要"，然后点击"继续"。',
      step5: '选择您想要与Apple ID一起使用iMessage和FaceTime的"主要"线路，然后点击"继续"。',
      step6: '为蜂窝网络/移动数据选择您的新eSIM套餐，然后点击"继续"。',
      accessTitle: '步骤 2/2 访问数据',
      access1:
        '前往"蜂窝网络/移动数据"，然后选择手机上最近下载的eSIM。打开"启用此线路"开关，然后为蜂窝网络/移动数据选择您的新eSIM套餐。',
      access2: '点击"网络选择"，禁用"自动"开关，然后选择出现的网络。如果您的eSIM连接到错误的网络。',
      access3: '点击"漫游"，然后为您的新eSIM套餐启用"数据漫游"。',
    },
    stepGuide: '前往分步指南',
  },
  copyright: {
    text: '版权所有@2025 GlocalMe uCloudlink集团有限公司。',
  },
  promo: {
    title: '我的优惠',
    available_promos: '可用优惠',
    unavailable_promos: '不可用优惠',
    promo_code: '优惠码',
    coupon: '优惠券',
    valid_until: '有效期至',
    best_savings: '最佳优惠',
    use_promo: '使用优惠',
  },
  checkout: {
    title: '结算',
    activate_later: '我可以稍后激活我的套餐吗？',
    activation_period_text:
      '所有套餐都有30天的激活期。如果您今天购买套餐但直到4月26日才激活，它将自动激活。',
    promo: '优惠码',
    my_promo: '我的优惠',
    apply: '应用',
    order_summary: '订单摘要',
    item: '项',
    subtotal: '小计',
    vip_discount: 'VIP 5%折扣',
    total: '总计',
    place_order: '下单',
  },
  payment: {
    title: '支付',
    failed_title: '支付失败',
    error_message: '抱歉，出现了一些问题。',
    error_description: '我们很抱歉，发生了一个错误。请稍后再试。',
    back_to_checkout: '返回结账页面',
  },
  order: {
    completed_title: '订单完成',
    thank_you: '感谢您的订购！',
    number: '订单号：',
    go_to_orders: '前往订单列表',
    how_to_install: '如何安装eSIM？',
    install_instruction: '您可以按照说明安装您的eSIM。',
    esim_details: 'eSIM详情',
  },
  refer: {
    title: '推荐与赚取',
    reward_title: '赠送5美元 & 获得5美元',
    subtitle: '与朋友分享GlocalMe',
    description:
      '每当您推荐的朋友注册并完成购买，您将获得5美元奖励。您的朋友在首次购买时可享受5美元优惠。任何作弊行为都将导致奖励暂停发放。',
    your_code: '您的推荐码',
    copy: '复制',
    copy_success: '复制成功',
    copy_failed: '复制失败',
    share_now: '立即分享',
    share_title: 'GlocalMe推荐计划',
    share_text: '使用我的推荐码{code}注册GlocalMe，获得5美元优惠！',
    share_manually: '请手动分享您的推荐码',
    reward_history: '奖励历史',
    invitation_rewards: '邀请奖励',
  },
  claim: {
    title: '嘿，想试试eSIM吗？',
    reward_title: '5美元和积分等您领取',
    subtitle: '与GlocalMe保持连接',
    code_label: '优惠码',
    copy: '复制',
    copy_success: '复制成功',
    copy_failed: '复制失败',
    sign_up: '注册并领取',
    how_to_use: '如何使用优惠码？',
    step1: '1. 点击"注册并领取"，几秒钟内创建账户。',
    step2: '2. 注册后兑换优惠码。',
    step3: '3. 然后使用折扣购买eSIM套餐。',
  },
  editPassword: {
    title: '修改密码',
    verificationMessage: '接下来，我们将向您的邮箱发送验证码。请输入我们发送的6位数验证码。',
    verificationCode: '验证码',
    sendCode: '发送验证码',
    newPassword: '新密码',
    retypePassword: '确认新密码',
    passwordRequirements:
      '密码必须为6-20个字符，并且包含以下至少两种：字母、数字或符号。同时不能是您之前使用过的密码。',
    save: '保存',
    sendingCode: '发送中...',
    codeSent: '验证码已发送',
    sendCodeFailed: '验证码发送失败',
    saving: '保存中...',
    saveSuccess: '密码更新成功',
    saveFailed: '密码更新失败',
  },
  editEmail: {
    title: '修改邮箱',
    verificationMessage: '接下来，我们将向您的邮箱发送验证码。请输入我们发送的6位数验证码。',
    verificationCode: '验证码',
    sendCode: '发送验证码',
    newEmail: '新邮箱',
    save: '保存',
    sendingCode: '发送中...',
    codeSent: '验证码已发送',
    sendCodeFailed: '验证码发送失败',
    saving: '保存中...',
    saveSuccess: '邮箱更新成功',
    saveFailed: '邮箱更新失败',
  },
  editName: {
    title: '编辑姓名',
    placeholder: '请输入您的姓名',
    cancel: '取消',
    save: '保存',
    saving: '保存中...',
    saveSuccess: '姓名更新成功',
    saveFailed: '姓名更新失败',
    nameRequired: '姓名不能为空',
  },
}
