import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { showToast } from 'vant'
import {
  getToken,
  setToken,
  getUserInfo,
  setUserInfo,
  clearAuth,
  isLoggedIn,
  type UserInfo
} from '@/utils/token'
import { quickUserLogin, registerUser, getVerificationCodeBySMS } from '@/api'
import type {
  QuickUserLoginRequest,
  RegisterUserRequest,
  GetVerificationCodeBySMSRequest
} from '@/api/LoginRegistration/types'
import { ChannelType, LangType, RegisterType } from '@/api/enums'
import { getClientConfig, getSMSConfig, API_CONFIG } from '@/config/api'
import CryptoJS from 'crypto-js'
const [ clientId, clientSecret ] = import.meta.env.VITE_API_URL;

// 模拟已注册的邮箱列表（用于演示）
const EXISTING_EMAILS = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']

export const useAuth = () => {
  const router = useRouter()

  // 表单状态
  const email = ref('')
  const password = ref('')
  const retypePassword = ref('')
  const verificationCode = ref('')
  const selectedCountry = ref('')
  const isLoading = ref(false)
  const showPassword = ref(false)
  const showRetypePassword = ref(false)
  const codeSent = ref(false)
  const countdown = ref(0)
  const currentStep = ref<'email' | 'register' | 'country'>('email')

  // 认证状态
  const user = ref<UserInfo | null>(getUserInfo())
  const token = ref<string | null>(getToken())

  // 计算属性
  const isAuthenticated = computed(() => isLoggedIn())

  // 检测当前环境
  const isMobile = computed(() => {
    if (typeof window === 'undefined') return false
    return window.location.pathname.includes('/mobile') ||
           window.location.port === '5174'
  })

  // 显示消息
  const showMessage = (message: string, type: 'success' | 'error' = 'success') => {
    if (isMobile.value) {
      showToast({
        message,
        type: type === 'error' ? 'fail' : 'success'
      })
    } else {
      if (type === 'error') {
        ElMessage.error(message)
      } else {
        ElMessage.success(message)
      }
    }
  }

  // MD5 加密密码
  const encryptPassword = (password: string): string => {
    return CryptoJS.MD5(password).toString()
  }

  // 验证状态
  const emailValid = computed(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email.value)
  })

  const passwordValid = computed(() => {
    return (
      password.value.length >= 6 &&
      password.value.length <= 20 &&
      /[a-zA-Z]/.test(password.value) &&
      /[0-9]/.test(password.value) &&
      /[!@#$%^&*(),.?":{}|<>]/.test(password.value)
    )
  })

  const passwordsMatch = computed(() => {
    return password.value === retypePassword.value && password.value.length > 0
  })

  const codeValid = computed(() => {
    return verificationCode.value.length === 6 && /^\d{6}$/.test(verificationCode.value)
  })

  const passwordError = computed(() => {
    if (retypePassword.value && !passwordsMatch.value) {
      return 'The passwords you entered do not match. Please try again.'
    }
    return ''
  })

  // 注册表单是否可以继续到地区选择
  const canContinueToCountry = computed(() => {
    return emailValid.value && passwordValid.value && passwordsMatch.value
  })

  // 最终注册是否可以提交
  const canSubmitRegister = computed(() => {
    return canContinueToCountry.value && selectedCountry.value
  })

  // 检查用户是否存在
  const checkUserExists = async (emailAddress: string): Promise<boolean> => {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 800))
    return EXISTING_EMAILS.includes(emailAddress.toLowerCase())
  }

  // 真实 API 登录
  const apiLogin = async (loginData: {
    userCode: string
    password: string
    randomId?: string
    randomCode?: string
  }) => {
    isLoading.value = true

    try {
      const clientConfig = getClientConfig()
      const params: QuickUserLoginRequest = {
        clientId: clientConfig.clientId,
        clientSecret: clientConfig.clientSecret,
        userCode: loginData.userCode,
        enterpriseCode: clientConfig.enterpriseCode,
        password: '92b9899e4fe67ec983b66a422caf6eca',
        channelType: isMobile.value ? ChannelType.APP : ChannelType.WEB,
        randomId: loginData.randomId || '',
        randomCode: loginData.randomCode || '',
        loginType: API_CONFIG.LOGIN_TYPE,
        custAttrList: [],
        checkMvnoCodeList: []
      }

      const response = await quickUserLogin(params)

      if (response?.accessToken) {
        // 保存 Token 和用户信息
        setToken(response.accessToken)
        const userInfo: UserInfo = {
          userId: response.userId,
          userCode: loginData.userCode,
          isBindPhone: response.isBindPhone,
          custAttrValueList: response.custAttrValueList
        }
        setUserInfo(userInfo)

        // 更新响应式状态
        token.value = response.accessToken
        user.value = userInfo

        showMessage('登录成功')

        // 跳转到首页
        router.push(isMobile.value ? '/mobile/' : '/')

        return { success: true, message: 'Login successful!', data: response }
      } else {
        throw new Error('登录失败，未获取到有效 Token')
      }
    } catch (error: any) {
      const message = error.message || 'Login failed. Please check your credentials.'
      showMessage(message, 'error')
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  // 兼容原有的登录方法（用于演示）
  const login = async (emailAddress: string, userPassword: string) => {
    isLoading.value = true
    try {
      // 模拟登录API调用
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // 检查邮箱是否存在
      const userExists = await checkUserExists(emailAddress)
      if (!userExists) {
        throw new Error('User not found')
      }

      return { success: true, message: 'Login successful!' }
    } catch (error) {
      return { success: false, message: 'Login failed. Please check your credentials.' }
    } finally {
      isLoading.value = false
    }
  }

  // 真实 API 注册
  const apiRegister = async (registerData: {
    userCode: string
    password: string
    countryCode: string
    msgCode?: string
    randomId?: string
    randomCode?: string
  }) => {
    isLoading.value = true

    try {
      const params: RegisterUserRequest = {
        langType: LangType.EN_US,
        registerType: RegisterType.PHONE,
        msgCode: registerData.msgCode,
        userCode: registerData.userCode,
        channelType: isMobile.value ? ChannelType.APP : ChannelType.WEB,
        countryCode: registerData.countryCode,
        password: encryptPassword(registerData.password),
        registerCountry: registerData.countryCode,
        randomId: registerData.randomId,
        randomCode: registerData.randomCode,
        sendActiveEmail: '0'
      }

      const response = await registerUser(params)

      if (response?.data?.customerId) {
        showMessage('注册成功，请登录')

        // 跳转到登录页
        router.push(isMobile.value ? '/mobile/#/login' : '/#/login')

        return { success: true, message: 'Registration successful!', data: response }
      } else {
        throw new Error('注册失败')
      }
    } catch (error: any) {
      const message = error.message || 'Registration failed. Please try again.'
      showMessage(message, 'error')
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  // 兼容原有的注册方法（用于演示）
  const register = async (emailAddress: string, userPassword: string, countryCode?: string) => {
    isLoading.value = true
    try {
      // 检查邮箱是否已存在
      const userExists = await checkUserExists(emailAddress)
      if (userExists) {
        return {
          success: false,
          message: 'This email is already registered. Please try logging in instead.',
          shouldRedirectToLogin: true,
        }
      }

      // 模拟注册API调用
      await new Promise((resolve) => setTimeout(resolve, 1000))

      return {
        success: true,
        message: 'Registration successful!',
        data: {
          email: emailAddress,
          country: countryCode,
        },
      }
    } catch (error) {
      return { success: false, message: 'Registration failed. Please try again.' }
    } finally {
      isLoading.value = false
    }
  }

  // 真实 API 发送验证码
  const apiSendVerificationCode = async (phone: string, businessType: string = API_CONFIG.BUSINESS_TYPE.REGISTER) => {
    try {
      const smsConfig = getSMSConfig(businessType)
      const params: GetVerificationCodeBySMSRequest = {
        langType: LangType.EN_US,
        phone,
        businessType: smsConfig.businessType,
        msgTemplateCode: smsConfig.msgTemplateCode
      }

      const response = await getVerificationCodeBySMS(params)

      if (response?.data) {
        showMessage('验证码发送成功')

        codeSent.value = true
        countdown.value = 60

        // 开始倒计时
        const timer = setInterval(() => {
          countdown.value--
          if (countdown.value <= 0) {
            clearInterval(timer)
          }
        }, 1000)

        return { success: true, message: 'Verification code sent successfully!', data: response }
      } else {
        throw new Error('验证码发送失败')
      }
    } catch (error: any) {
      const message = error.message || 'Failed to send verification code. Please try again.'
      showMessage(message, 'error')
      return { success: false, message }
    }
  }

  // 兼容原有的发送验证码方法（用于演示）
  const sendVerificationCode = async (emailAddress: string) => {
    isLoading.value = true
    try {
      // 模拟发送验证码API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      codeSent.value = true
      countdown.value = 60

      // 开始倒计时
      const timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)

      return { success: true, message: 'Verification code sent to your email!' }
    } catch (error) {
      return { success: false, message: 'Failed to send verification code. Please try again.' }
    } finally {
      isLoading.value = false
    }
  }

  // 重置密码
  const resetPassword = async (emailAddress: string, code: string, newPassword: string) => {
    isLoading.value = true
    try {
      // 模拟重置密码API
      await new Promise((resolve) => setTimeout(resolve, 1500))

      return { success: true, message: 'Password reset successful!' }
    } catch (error) {
      return { success: false, message: 'Failed to reset password. Please try again.' }
    } finally {
      isLoading.value = false
    }
  }

  // 重置表单
  const resetForm = () => {
    email.value = ''
    password.value = ''
    retypePassword.value = ''
    verificationCode.value = ''
    selectedCountry.value = ''
    showPassword.value = false
    showRetypePassword.value = false
    codeSent.value = false
    countdown.value = 0
    currentStep.value = 'email'
  }

  // 继续到地区选择步骤
  const continueToCountrySelection = () => {
    if (canContinueToCountry.value) {
      currentStep.value = 'country'
      return true
    }
    return false
  }

  // 返回到注册表单
  const backToRegisterForm = () => {
    currentStep.value = 'register'
  }

  // 设置选中的国家
  const setSelectedCountry = (countryCode: string) => {
    selectedCountry.value = countryCode
  }

  // 登出
  const logout = () => {
    clearAuth()
    token.value = null
    user.value = null

    showMessage('已退出登录')

    // 跳转到登录页
    router.push(isMobile.value ? '/mobile/#/login' : '/#/login')
  }

  // 检查登录状态
  const checkAuth = () => {
    if (!isAuthenticated.value) {
      router.push(isMobile.value ? '/mobile/#/login' : '/#/login')
      return false
    }
    return true
  }

  // 社交登录
  const socialLogin = async (provider: 'google' | 'apple' | 'mobile') => {
    isLoading.value = true
    try {
      // 模拟社交登录API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      return { success: true, message: `${provider} login successful!` }
    } catch (error) {
      return { success: false, message: `${provider} login failed. Please try again.` }
    } finally {
      isLoading.value = false
    }
  }

  return {
    // 状态
    email,
    password,
    retypePassword,
    verificationCode,
    selectedCountry,
    isLoading,
    showPassword,
    showRetypePassword,
    codeSent,
    countdown,
    currentStep,
    user,
    token,

    // 计算属性
    emailValid,
    passwordValid,
    passwordsMatch,
    codeValid,
    passwordError,
    canContinueToCountry,
    canSubmitRegister,
    isAuthenticated,
    isMobile,

    // 方法
    checkUserExists,
    login,
    register,
    sendVerificationCode,
    resetPassword,
    resetForm,
    socialLogin,
    continueToCountrySelection,
    backToRegisterForm,
    setSelectedCountry,

    // 新的 API 方法
    apiLogin,
    apiRegister,
    apiSendVerificationCode,
    logout,
    checkAuth,
    showMessage,
    encryptPassword,
  }
}
