import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { showToast } from 'vant'
import {
  getToken,
  setToken,
  getUserInfo,
  setUserInfo,
  clearAuth,
  isLoggedIn,
  type UserInfo,
} from '@/utils/token'
import { quickUserLogin, registerUser, getVerificationCodeBySMS } from '@/api'
import type {
  QuickUserLoginRequest,
  RegisterUserRequest,
  GetVerificationCodeBySMSRequest,
} from '@/api/LoginRegistration/types'
import { ChannelType, LangType, RegisterType } from '@/api/enums'
import { getClientConfig, getSMSConfig, API_CONFIG } from '@/config/api'
import { useUserInfoStore } from '@/stores/userInfo'
import CryptoJS from 'crypto-js'

// 模拟已注册的邮箱列表（用于演示）
const EXISTING_EMAILS = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']

export const useAuth = () => {
  const router = useRouter()
  const userInfoStore = useUserInfoStore()

  // 表单状态
  const email = ref('')
  const password = ref('')
  const retypePassword = ref('')
  const verificationCode = ref('')
  const selectedCountry = ref('')
  const isLoading = ref(false)
  const showPassword = ref(false)
  const showRetypePassword = ref(false)
  const codeSent = ref(false)
  const countdown = ref(0)
  const currentStep = ref<'email' | 'register' | 'country'>('email')

  // 注册步骤状态
  const registerStep = ref<'form' | 'country'>('form')

  // 认证状态
  const user = ref<UserInfo | null>(getUserInfo())
  const token = ref<string | null>(getToken())

  // 计算属性
  const isAuthenticated = computed(() => isLoggedIn())

  // 检测当前环境
  const isMobile = computed(() => {
    if (typeof window === 'undefined') return false
    return window.location.pathname.includes('/mobile') || window.location.port === '5174'
  })

  // 显示消息
  const showMessage = (message: string, type: 'success' | 'error' = 'success') => {
    if (isMobile.value) {
      showToast({
        message,
        type: type === 'error' ? 'fail' : 'success',
      })
    } else {
      if (type === 'error') {
        ElMessage.error(message)
      } else {
        ElMessage.success(message)
      }
    }
  }

  // MD5 加密密码
  const encryptPassword = (password: string): string => {
    return CryptoJS.MD5(password).toString()
  }

  // 验证状态
  const emailValid = computed(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email.value)
  })

  const passwordValid = computed(() => {
    return (
      password.value.length >= 6 &&
      password.value.length <= 20 &&
      /[a-zA-Z]/.test(password.value) &&
      /[0-9]/.test(password.value) &&
      /[!@#$%^&*(),.?":{}|<>]/.test(password.value)
    )
  })

  const passwordsMatch = computed(() => {
    return password.value === retypePassword.value && password.value.length > 0
  })

  const codeValid = computed(() => {
    return verificationCode.value.length === 6 && /^\d{6}$/.test(verificationCode.value)
  })

  const passwordError = computed(() => {
    if (retypePassword.value && !passwordsMatch.value) {
      return 'The passwords you entered do not match. Please try again.'
    }
    return ''
  })

  // 注册表单是否可以继续到地区选择
  const canContinueToCountry = computed(() => {
     console.log(emailValid,'emailValid')
    return emailValid.value && passwordValid.value && passwordsMatch.value
  })

  // 最终注册是否可以提交
  const canSubmitRegister = computed(() => {
    return canContinueToCountry.value && selectedCountry.value
  })

  // 检查用户是否存在
  const checkUserExists = async (emailAddress: string): Promise<boolean> => {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 800))
    return EXISTING_EMAILS.includes(emailAddress.toLowerCase())
  }

  // 真实 API 登录
  const apiLogin = async (loginData: {
    userCode: string
    password: string
    randomId?: string
    randomCode?: string
  }) => {
    isLoading.value = true

    try {
      const clientConfig = getClientConfig()
      const params: QuickUserLoginRequest = {
        clientId: clientConfig.clientId,
        clientSecret: clientConfig.clientSecret,
        userCode: loginData.userCode,
        enterpriseCode: clientConfig.enterpriseCode,
        password: encryptPassword(loginData.password),
        channelType: isMobile.value ? ChannelType.APP : ChannelType.WEB,
        randomId: loginData.randomId || '',
        randomCode: loginData.randomCode || '',
        loginType: API_CONFIG.LOGIN_TYPE,
        custAttrList: [],
        checkMvnoCodeList: [],
      }

      const response = await quickUserLogin(params)

      if (response?.accessToken) {
        // 保存 Token 和用户信息
        setToken(response.accessToken)
        const userInfo: UserInfo = {
          userId: response.userId,
          userCode: loginData.userCode,
          isBindPhone: response.isBindPhone,
          custAttrValueList: response.custAttrValueList,
        }
        setUserInfo(userInfo)

        // 更新响应式状态
        token.value = response.accessToken
        user.value = userInfo

        showMessage('登录成功')

        // 获取用户详细信息
        await userInfoStore.fetchUserInfo()

        // 跳转到首页
        router.push(isMobile.value ? '/mobile/' : '/')

        return { success: true, message: 'Login successful!', data: response }
      } else {
        throw new Error('登录失败，未获取到有效 Token')
      }
    } catch (error: any) {
      const message = error.message || 'Login failed. Please check your credentials.'
      showMessage(message, 'error')
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  // 兼容原有的登录方法（用于演示）
  const login = async (emailAddress: string, userPassword: string) => {
    isLoading.value = true
    try {
      // 模拟登录API调用
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // 检查邮箱是否存在
      const userExists = await checkUserExists(emailAddress)
      if (!userExists) {
        throw new Error('User not found')
      }

      return { success: true, message: 'Login successful!' }
    } catch (error) {
      return { success: false, message: 'Login failed. Please check your credentials.' }
    } finally {
      isLoading.value = false
    }
  }

  // 真实 API 注册
  const apiRegister = async (registerData: {
    userCode: string
    password: string
    countryCode: string
    msgCode?: string
    randomId?: string
    randomCode?: string
  }) => {
    isLoading.value = true

    try {
      const params: RegisterUserRequest = {
        langType: LangType.EN_US,
        registerType: RegisterType.EMAIL,
        msgCode: registerData.msgCode,
        userCode: registerData.userCode,
        channelType: isMobile.value ? ChannelType.APP : ChannelType.WEB,
        password: encryptPassword(registerData.password),
        registerCountry: registerData.countryCode,
        enterpriseCode: getClientConfig().enterpriseCode,
        randomId: registerData.randomId,
        randomCode: registerData.randomCode,
        sendActiveEmail: '0',
      }

      const response = await registerUser(params)

      if (response?.data?.customerId) {
        apiLogin({ userCode: registerData.userCode,
          password: encryptPassword(registerData.password),
          randomId?: registerData.randomId,
          randomCode?: registerData.randomCode,})
        // 跳转到登录页
        router.push(isMobile.value ? '/mobile/#/login' : '/#/login')

        return { success: true, message: 'Registration successful!', data: response }
      } else {
        throw new Error('注册失败')
      }
    } catch (error: any) {
      const message = error.message || 'Registration failed. Please try again.'
      showMessage(message, 'error')
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }
  // 兼容原有的注册方法（用于演示，但调用真实 API）
  const register = async (emailAddress: string, userPassword: string, countryCode?: string) => {
    isLoading.value = true
    try {
      // // 检查邮箱是否已存在
      // const userExists = await checkUserExists(emailAddress)
      // if (userExists) {
      //   return {
      //     success: false,
      //     message: 'This email is already registered. Please try logging in instead.',
      //     shouldRedirectToLogin: true,
      //   }
      // }

      // 调用真实的注册 API
      const result = await apiRegister({
        userCode: emailAddress, // 使用邮箱作为用户代码
        password: userPassword,
        countryCode: countryCode || '+1', // 默认使用 +1
        msgCode: '000000' // 演示用的验证码，实际应该从用户输入获取
      })

      if (result.success) {
        return {
          success: true,
          message: 'Registration successful! Please log in.',
          data: {
            email: emailAddress,
            country: countryCode,
          },
        }
      } else {
        return { success: false, message: result.message }
      }
    } catch (error: any) {
      return { success: false, message: error.message || 'Registration failed. Please try again.' }
    } finally {
      isLoading.value = false
    }
  }

  // 真实 API 发送验证码
  const apiSendVerificationCode = async (
    phone: string,
    businessType: string = API_CONFIG.BUSINESS_TYPE.REGISTER,
  ) => {
    try {
      const smsConfig = getSMSConfig(businessType)
      const params: GetVerificationCodeBySMSRequest = {
        langType: LangType.EN_US,
        phone,
        businessType: smsConfig.businessType,
        msgTemplateCode: smsConfig.msgTemplateCode,
      }

      const response = await getVerificationCodeBySMS(params)

      if (response?.data) {
        showMessage('验证码发送成功')

        codeSent.value = true
        countdown.value = 60

        // 开始倒计时
        const timer = setInterval(() => {
          countdown.value--
          if (countdown.value <= 0) {
            clearInterval(timer)
          }
        }, 1000)

        return { success: true, message: 'Verification code sent successfully!', data: response }
      } else {
        throw new Error('验证码发送失败')
      }
    } catch (error: any) {
      const message = error.message || 'Failed to send verification code. Please try again.'
      showMessage(message, 'error')
      return { success: false, message }
    }
  }
  // 兼容原有的发送验证码方法（用于演示）
  const sendVerificationCode = async (emailAddress: string) => {
    isLoading.value = true
    try {
      // 模拟发送验证码API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      codeSent.value = true
      countdown.value = 60

      // 开始倒计时
      const timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)

      return { success: true, message: 'Verification code sent to your email!' }
    } catch (error) {
      return { success: false, message: 'Failed to send verification code. Please try again.' }
    } finally {
      isLoading.value = false
    }
  }

  // 重置密码
  const resetPassword = async (emailAddress: string, code: string, newPassword: string) => {
    isLoading.value = true
    try {
      // 模拟重置密码API
      await new Promise((resolve) => setTimeout(resolve, 1500))

      return { success: true, message: 'Password reset successful!' }
    } catch (error) {
      return { success: false, message: 'Failed to reset password. Please try again.' }
    } finally {
      isLoading.value = false
    }
  }

  // 重置表单
  const resetForm = () => {
    email.value = ''
    password.value = ''
    retypePassword.value = ''
    verificationCode.value = ''
    selectedCountry.value = ''
    showPassword.value = false
    showRetypePassword.value = false
    codeSent.value = false
    countdown.value = 0
    currentStep.value = 'email'
  }

  // 登出
  const logout = async () => {
    try {
      // 调用 store 的退出登录方法
      await userInfoStore.logout()

      // 更新本地状态
      token.value = null
      user.value = null

      showMessage('已退出登录')

      // 跳转到登录页
      router.push(isMobile.value ? '/mobile/#/login' : '/#/login')
    } catch (error) {
      console.error('Logout error:', error)
      // 即使出错也要清除本地状态
      clearAuth()
      token.value = null
      user.value = null
      router.push(isMobile.value ? '/mobile/#/login' : '/#/login')
    }
  }

  // 检查登录状态
  const checkAuth = () => {
    if (!isAuthenticated.value) {
      router.push(isMobile.value ? '/mobile/#/login' : '/#/login')
      return false
    }
    return true
  }

  // 社交登录
  const socialLogin = async (provider: 'google' | 'apple' | 'mobile') => {
    isLoading.value = true
    try {
      // 模拟社交登录API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      return { success: true, message: `${provider} login successful!` }
    } catch (error) {
      return { success: false, message: `${provider} login failed. Please try again.` }
    } finally {
      isLoading.value = false
    }
  }

  // 注册步骤控制
  const continueToCountrySelection = () => {
    registerStep.value = 'country'
  }

  const backToRegisterForm = () => {
    registerStep.value = 'form'
  }

  const setSelectedCountry = (country: string) => {
    selectedCountry.value = country
  }

  return {
    // 状态
    email,
    password,
    retypePassword,
    verificationCode,
    selectedCountry,
    isLoading,
    showPassword,
    showRetypePassword,
    codeSent,
    countdown,
    currentStep,
    registerStep,
    user,
    token,

    // 计算属性
    emailValid,
    passwordValid,
    passwordsMatch,
    codeValid,
    passwordError,
    canContinueToCountry,
    canSubmitRegister,
    isAuthenticated,
    isMobile,

    // 方法
    checkUserExists,
    login,
    register,
    sendVerificationCode,
    resetPassword,
    resetForm,
    socialLogin,
    continueToCountrySelection,
    backToRegisterForm,
    setSelectedCountry,

    // 新的 API 方法
    apiLogin,
    apiRegister,
    apiSendVerificationCode,
    logout,
    checkAuth,
    showMessage,
    encryptPassword,
  }
}
