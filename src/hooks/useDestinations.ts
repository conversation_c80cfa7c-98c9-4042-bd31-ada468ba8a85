import { ref, computed, readonly } from 'vue'
import { useRouter } from 'vue-router'
import { useRequest } from 'vue-hooks-plus'
import { getPopularDestinations, DestinationKeyword } from '@/api'
import type { PopularDestination } from '@/api/Country/types'
import { DestinationType } from '@/data/destinations'
import { paramsWrap } from '@/api/utils'

const getIconUrl = function (obj) {
  if (!obj.iso2) return
  const icon_path =
    obj.keyType === DestinationType.REGION.toUpperCase()
      ? 'imagesForPopRegion'
      : 'imagesForPopCountry'
  return (
    'https://mph5.ucloudlink.com/appweb/heyesim/ImageLibrary/' + icon_path + '/' + obj.iso2 + '.png'
  )
}

/**
 * 热门目的地 Hook
 * 提供热门目的地数据获取和过滤功能
 */
export function useDestinations() {
  // 响应式数据
  const destinations = ref<PopularDestination[]>([])
  const activeTab = ref(DestinationType.LOCAL)
  const letterFilter = ref('')

  // 获取热门目的地数据
  const { loading, error, refresh } = useRequest(
    () => getPopularDestinations(paramsWrap({ type: DestinationKeyword.HOT_ESIM_KEYWORD })),
    {
      staleTime: 50000,
      onSuccess: (data) => {
        destinations.value = (data?.data || []).map((item) => {
          item.iconUrl = getIconUrl(item)
          return item
        })
      },
      onError: (err) => {
        console.error('获取热门目的地失败:', err)
      },
    },
  )

  // 根据当前选中的标签和首字母过滤目的地列表
  const filteredDestinations = computed(() => {
    let temp = destinations.value

    // 按类型过滤（本地/区域）
    if (activeTab.value === DestinationType.REGION) {
      temp = temp.filter((d) => (d.keyType || '').toLowerCase() === DestinationType.REGION)
    }

    // 按首字母过滤
    if (letterFilter.value) {
      temp = temp.filter((d) => {
        const firstLetter = (d.value || '').charAt(0).toUpperCase()
        return letterFilter.value.includes(firstLetter)
      })
    }

    return temp
  })

  // 获取指定数量的目的地列表
  const getDestinationsByLimit = (limit: number) => {
    return filteredDestinations.value.slice(0, limit)
  }

  // 切换标签
  const setActiveTab = (tab: DestinationType) => {
    activeTab.value = tab
  }

  // 设置首字母过滤
  const setLetterFilter = (letters: string) => {
    letterFilter.value = letters
  }

  // 重置数据
  const reset = () => {
    destinations.value = []
    activeTab.value = DestinationType.LOCAL
    letterFilter.value = ''
  }

  return {
    // 响应式数据
    destinations: readonly(destinations),
    activeTab: readonly(activeTab),
    letterFilter: readonly(letterFilter),
    loading,
    error,

    // 计算属性
    filteredDestinations,

    // 方法
    setActiveTab,
    setLetterFilter,
    getDestinationsByLimit,
    refresh,
    reset,
  }
}

/**
 * 目的地导航 Hook
 * 提供目的地相关的路由导航功能
 */
export function useDestinationNavigation() {
  const { push } = useRouter()

  // 跳转到套餐详情页
  const goToPlanDetail = (item: PopularDestination) => {
    push({
      name: 'plan-detail',
      params: {
        pid: item.iso2,
      },
      query: {
        name: item.value,
      },
    })
  }

  // 跳转到套餐列表页
  const goToShopPlans = () => {
    push({ name: 'shop-plans' })
  }

  return {
    goToPlanDetail,
    goToShopPlans,
  }
}
