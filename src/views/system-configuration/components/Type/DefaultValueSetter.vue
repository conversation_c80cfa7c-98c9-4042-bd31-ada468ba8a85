<script setup lang="ts">
import {
  ElInput,
  ElFormItem,
  ElButton,
  ElAlert,
  ElSwitch,
  ElDatePicker,
  ElInputNumber,
  ElSelect,
  ElRadio,
  ElCheckbox
} from 'element-plus'
import {
  keyMap,
  sourceEnum,
  timeMap,
  constraintMap
} from '@/views/system-configuration/Config/help'
import { ApiSelect } from '@/components/ApiSelect'
import { getDictByCodeApi, queryCascade } from '@/api/common'
import OssUpload from '@/components/Upload/OssUpload.vue'
import AddSourceDialog from './AddSourceDialog.vue'

const { typeMap, componentRap } = constraintMap

// TypeScript interfaces
interface ConstraintItem {
  value: any
  [key: string]: any
}

interface ConstraintList {
  [key: string]: ConstraintItem
}

interface SourceOptions {
  value?: string
  label?: string
  type?: string
  [key: string]: any
}

interface SourceData {
  value: string
  label: string
  type: string
}

type DefaultValueType = string | number | boolean | any[] | Record<string, any>
// Props with proper typing
interface Props {
  constraintType: string
  constraintList: ConstraintList
  defaultValue?: DefaultValueType
  defaultName?: string
  options?: SourceOptions
  disabled?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  defaultValue: '',
  defaultName: '',
  options: () => ({}),
  disabled: false,
  loading: false
})

// Emits with proper typing
interface Emits {
  (e: 'update:defaultValue', value: DefaultValueType): void
  (e: 'update:defaultName', value: string): void
  (e: 'update:options', value: SourceOptions): void
}

const emit = defineEmits<Emits>()

const localDefaultValue = computed({
  get: (): DefaultValueType => props.defaultValue,
  set: (val: DefaultValueType) => emit('update:defaultValue', val)
})

const localDefaultName = computed({
  get: (): string => props.defaultName || '',
  set: (val: string) => emit('update:defaultName', val)
})

const localOptions = computed({
  get: (): SourceOptions => props.options || {},
  set: (val: SourceOptions) => emit('update:options', val)
})

// 根据约束类型确定要渲染的组件
const renderComponent = computed(() => {
  if (props.constraintType === typeMap.OBJECT) {
    return ApiSelect
  }

  // 确保组件存在，否则返回默认的 ElInput
  const component = componentRap[props.constraintType.toUpperCase()]
  if (!component) {
    console.warn(`未找到约束类型 ${props.constraintType} 对应的组件，使用默认的 ElInput`)
    return ElInput
  }

  return component
})

// 计算组件需要的属性
const bindProps = computed((): Record<string, any> => {
  try {
    let propsMap: Record<string, any> = {}

    // 从约束列表中提取属性
    if (props.constraintList) {
      Object.keys(props.constraintList).forEach((key: string) => {
        if (key !== 'options' && key !== 'component') {
          propsMap[key] = props.constraintList[key]?.value
        }
      })
    }

    propsMap = {
      ...propsMap,
      min: props.constraintList[keyMap.min]?.value || props.constraintList[keyMap.minLength]?.value,
      max: props.constraintList[keyMap.max]?.value || props.constraintList[keyMap.maxLength]?.value
    }
    // 对于选择类型，配置API选择器
    if (props.constraintType === typeMap.OBJECT && localOptions.value?.value) {
      return {
        ...propsMap,
        props: { ...propsMap },
        'collapse-tags': true,
        'collapse-tags-tooltip': true,
        apiConfig: {
          api: localOptions.value?.type === sourceEnum.enum ? getDictByCodeApi : queryCascade,
          config:
            localOptions.value?.type === sourceEnum.enum
              ? {
                  label: 'label',
                  value: 'value'
                }
              : {
                  label: 'categoryCnName',
                  value: 'categoryCode',
                  children: 'childList'
                }
        },
        params:
          localOptions.value?.type === sourceEnum.enum
            ? {
                dictCode: localOptions.value?.value
              }
            : {
                type: localOptions.value?.type,
                value: localOptions.value?.value
              },
        disabled: props.disabled,
        component: componentRap?.[props.constraintList?.component?.value] || ElSelect,
        childComponent:
          props.constraintList?.component?.value === 'RADIOGROUP'
            ? ElRadio
            : props.constraintList?.component?.value === 'CHECKBOXGROUP'
            ? ElCheckbox
            : ''
      }
    }

    // 其他类型返回基本属性
    return {
      ...propsMap,
      disabled: props.disabled
    }
  } catch (error) {
    console.error('计算 bindProps 时出错:', error)
    return { disabled: props.disabled }
  }
})

// 根据约束类型获取适当的占位符文本
const getPlaceholder = computed((): string => {
  const componentMap: Record<string, string> = {
    [typeMap.STRING]: '请输入文本',
    [typeMap.INT]: '请输入数字',
    [typeMap.FLOAT]: '请输入数字',
    [typeMap.RICH_TEXT]: '请输入内容',
    [typeMap.DATE_TIME]: '请选择日期',
    [typeMap.OBJECT]: '请选择选项'
  }

  return componentMap[props.constraintType] || '请输入'
})

// 选择来源对话框控制
const sourceDialogVisible = ref<boolean>(false)

// 打开选择来源对话框
const openSourceDialog = (): void => {
  sourceDialogVisible.value = true
}

// 处理来源选择
const handleSourceSelect = (sourceData: SourceData): void => {
  localOptions.value = sourceData
  sourceDialogVisible.value = false
}
watch(
  () => props.options,
  (newOptions: SourceOptions) => {
    console.log('检测到选项变化:', newOptions)
  },
  {
    deep: true
  }
)
</script>

<template>
  <div class="default-value-setter">
    <!-- 根据约束类型渲染不同的输入组件 -->
    <div class="component-container">
      <ElFormItem label="默认值" v-if="constraintType !== typeMap.ATTACHMENT">
        <!-- 对象类型（下拉选择） -->
        <template v-if="constraintType === typeMap.OBJECT">
          <div class="source-selector">
            <ElButton @click="openSourceDialog" :disabled="disabled" size="small">
              选择数据来源
            </ElButton>
            <span class="source-value">{{ localOptions?.label || localOptions?.value }}</span>
          </div>
          <div style="width: 100%">
            <component
              v-if="localOptions?.value"
              :is="renderComponent"
              v-model="localDefaultValue"
              :key="JSON.stringify(constraintList)"
              v-bind="bindProps"
              :placeholder="getPlaceholder"
            />
            <ElAlert v-else-if="!localOptions?.value" type="info" :closable="false" show-icon>
              请先选择数据来源
            </ElAlert>
          </div>
        </template>
        <!-- 布尔类型（开关） -->
        <template v-else-if="constraintType === typeMap.BOOLEAN">
          <ElSwitch v-model="localDefaultValue" :disabled="disabled" />
        </template>

        <!-- 上传类型 -->
        <template v-else-if="constraintType === typeMap.ATTACHMENT">
          <OssUpload
            v-model="localDefaultValue"
            :limit="constraintList[keyMap.maxLength]?.value || 5"
            :size-limit="constraintList[keyMap.maxSize]?.value || 1024 * 1024 * 50"
            :accept="constraintList[keyMap.accept]?.value || 'image/*'"
            drag
            :listType="constraintList[keyMap.dataType]?.value === 'IMAGE' ? 'picture-card' : 'text'"
            :disabled="disabled"
          />
        </template>

        <!-- 富文本类型 -->
        <template v-else-if="constraintType === typeMap.RICH_TEXT">
          <ElInput
            type="textarea"
            v-model="localDefaultValue"
            :rows="constraintList[keyMap.rows]?.value || 3"
            :maxlength="constraintList[keyMap.maxLength]?.value"
            :show-word-limit="!!constraintList[keyMap.maxLength]?.value"
            :placeholder="getPlaceholder"
            :disabled="disabled"
          />
        </template>

        <!-- 日期时间类型 -->
        <template v-else-if="constraintType === typeMap.DATE_TIME">
          <ElDatePicker
            v-model="localDefaultValue"
            :type="constraintList[keyMap.isRange]?.value ? 'datetimerange' : 'date'"
            :placeholder="getPlaceholder"
            :format="timeMap?.[constraintList[keyMap.format]?.value] || 'YYYY-MM-DD'"
            :value-format="timeMap?.[constraintList[keyMap.format]?.value] || 'YYYY-MM-DD'"
            :disabled="disabled"
          />
        </template>

        <!-- 数字类型 -->
        <template v-else-if="constraintType === typeMap.INT || constraintType === typeMap.FLOAT">
          <!--分为两种情况，如果是区间类型是两个datePicker,如果不是区间类型是一个datePicker-->
          <div v-if="constraintList[keyMap.isRange]?.value">
            <ElInputNumber
              v-model="localDefaultValue[0]"
              :min="constraintList[keyMap.min]?.value"
              :max="constraintList[keyMap.max]?.value"
              :precision="
                constraintType === 'FLOAT' ? constraintList[keyMap.precision]?.value || 2 : ''
              "
              :step="constraintList[keyMap.step]?.value || 1"
              :placeholder="getPlaceholder"
              :disabled="disabled"
            />
            <span class="range-separator">~</span>
            <ElInputNumber
              v-model="localDefaultValue[1]"
              :min="constraintList[keyMap.min]?.value"
              :max="constraintList[keyMap.max]?.value"
              :precision="
                constraintType === 'FLOAT' ? constraintList[keyMap.precision]?.value || 2 : ''
              "
              :step="constraintList[keyMap.step]?.value || 1"
            />
          </div>
          <ElInputNumber
            v-else
            v-model="localDefaultValue"
            :min="constraintList[keyMap.min]?.value"
            :max="constraintList[keyMap.max]?.value"
            :precision="
              constraintType === 'FLOAT' ? constraintList[keyMap.precision]?.value || 2 : ''
            "
            :step="constraintList[keyMap.step]?.value || 1"
            :placeholder="getPlaceholder"
            :disabled="disabled"
          />
        </template>

        <!-- 默认文本类型 -->
        <template v-else>
          <component
            v-if="!loading"
            :is="renderComponent"
            v-model="localDefaultValue"
            v-bind="bindProps"
            :placeholder="getPlaceholder"
            :disabled="disabled"
        /></template>
      </ElFormItem>
    </div>

    <!-- 默认别名输入 -->
    <div class="default-name-container">
      <ElFormItem label="默认别名">
        <ElInput v-model="localDefaultName" placeholder="请输入默认别名" :disabled="disabled" />
      </ElFormItem>
    </div>

    <!-- 选择来源对话框 -->
    <AddSourceDialog v-model="sourceDialogVisible" @submit="handleSourceSelect" />
  </div>
</template>
<style scoped lang="less">
.default-value-setter {
  width: 100%;

  .component-container {
    margin-bottom: 16px;
  }

  .source-selector {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .source-value {
      margin-left: 12px;
      font-size: 14px;
      color: #606266;
    }
  }

  .default-name-container {
    margin-top: 16px;
  }
}
</style>
