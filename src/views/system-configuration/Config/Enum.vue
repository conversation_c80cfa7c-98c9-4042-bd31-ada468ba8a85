<script setup lang="ts">
import { VxeGridInstance, VxeGridProps } from 'vxe-table'
import { scrollProp } from '@/plugins/vxeTable'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { Icon } from '@/components/Icon'
import {
  EnumApi,
  EnumTypeApi,
  pageClassificationItem,
  saveClassificationItem,
  updateClassificationItem,
  deleteClassificationItem
} from '@/api/systemConfiguration/Enum'
import Left from '@/views/system-configuration/components/Left.vue'
import EnumItemDialog from '@/views/system-configuration/components/EnumItemDialog.vue'
import type { AdvancedCondition } from '@/components/AdvancedSearchForm'
import { EnumConfig, LeftType } from '@/views/system-configuration/Config/help'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import type { selectType } from '@/views/system-configuration/Config/help'
import { ElPagination } from 'element-plus'
import { watchDebounced } from '@vueuse/core'
defineOptions({
  name: 'Enum'
})

const enumItemTableRef = ref<VxeGridInstance>()
const pagerRef = ref<InstanceType<typeof ElPagination>>()
const enumItemList = ref<EnumApi.Row[]>([])
const enumItemLoading = ref(false)
const enumItemPager = reactive({
  current: 1,
  size: 10,
  total: 0
})
const maxHeight = useTableHeight({ tableRef: enumItemTableRef, pagerRef })
//类型选择
const selectedEnumType = ref<selectType | null>(null)
//枚举修改新增
const enumItemDialogVisible = ref(false)
const enumItemDialogMode = ref<'add' | 'edit'>('add')
const enumItemDialogData = ref<EnumApi.Row>({})

//普通数据
const formData = reactive<EnumApi.PageSearch>({
  enumeratedValue: '',
  enumeratedDesc: '',
  enumeratedClassificationId: ''
})
//高级搜索数据
const advancedConditions = ref<AdvancedCondition[]>([])

const tableOptions = computed(
  () =>
    ({
      border: true,
      showOverflow: true,
      height: maxHeight.value - 55,
      loading: enumItemLoading.value,
      data: enumItemList.value,
      columns: EnumConfig.EnumTableConfig,
      ...scrollProp
    } as VxeGridProps)
)

// 查询
const fetchEnumItems = async () => {
  if (enumItemLoading.value) return
  enumItemLoading.value = true
  try {
    const params = getParams()
    const [error, result] = await pageClassificationItem(params)
    if (error === null && result) {
      enumItemList.value = result.data?.records || []
      enumItemPager.total = result.data?.total || 0
    } else {
      ElMessage.error('获取枚举值列表失败')
    }
  } catch (error) {
    console.error('获取枚举值列表出错:', error)
    ElMessage.error('获取枚举值列表出错')
  } finally {
    enumItemLoading.value = false
  }
}
//新增
const handleAddEnumItem = () => {
  enumItemDialogMode.value = 'add'
  enumItemDialogData.value = {
    enumeratedClassificationId: selectedEnumType.value?.id
  }
  enumItemDialogVisible.value = true
}

// 编辑
const handleEditEnumItem = (row: EnumApi.Row) => {
  enumItemDialogMode.value = 'edit'
  enumItemDialogData.value = { ...row }
  enumItemDialogVisible.value = true
}

// 删除
const handleDeleteEnumItem = (row: EnumApi.Row) => {
  ElMessageBox.confirm('确定要删除该枚举值吗？删除后将无法恢复', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        const [error, result] = await deleteClassificationItem({ id: String(row.id) })
        if (error === null && result && result.success) {
          ElMessage.success('删除成功')
          fetchEnumItems()
        }
      } catch (error) {
        console.error('删除枚举值出错:', error)
        ElMessage.error('删除枚举值出错')
      }
    })
    .catch(() => {})
}

// 保存
const handleSaveEnumItem = async (data: EnumApi.Row, callback: () => Promise<any>) => {
  try {
    let error, result
    if (enumItemDialogMode.value === 'add') {
      ;[error, result] = await saveClassificationItem(data)
    } else {
      ;[error, result] = await updateClassificationItem(data)
    }

    if (error === null && result && result.success) {
      ElMessage.success(enumItemDialogMode.value === 'add' ? '添加成功' : '修改成功')
      enumItemDialogVisible.value = false
      fetchEnumItems()
    }
    callback()
  } catch (error) {
    console.error('保存枚举值出错:', error)
    ElMessage.error('保存枚举值出错')
    callback()
  }
}
const handleSearch = () => {
  enumItemPager.current = 1
  fetchEnumItems()
}
const getParams = (): EnumApi.pageRequest => {
  const params: EnumApi.pageRequest = {
    current: enumItemPager.current,
    size: enumItemPager.size,
    enumeratedClassificationId:
      selectedEnumType.value?.id === '-1' ? '' : String(selectedEnumType.value?.id || ''),
    enumeratedDesc: formData.enumeratedDesc,
    enumeratedValue: formData.enumeratedValue
  }
  if (advancedConditions.value.length > 0) {
    params.conditionList = advancedConditions.value
  }

  return params
}
const { handleExport: exportFn, loading: exportLoading } = useOmsExport(false)
const downloadFunc = () => {
  const selected: EnumTypeApi.Row[] | undefined = enumItemTableRef.value?.getCheckboxRecords()
  let reqParam: string
  if (selected && selected?.length > 0) {
    reqParam = JSON.stringify({ idList: selected.map((e) => e.id) })
  } else {
    reqParam = JSON.stringify(getParams())
  }

  exportFn({
    exportType: 'enumeratedClassification-export',
    reqParam
  })
}
watch(
  () => selectedEnumType.value,
  () => {
    fetchEnumItems()
  }
)
onMounted(() => {
  watchDebounced(
    formData,
    () => {
      handleSearch()
    },
    { deep: true, debounce: 400 }
  )
})
</script>

<template>
  <EnumItemDialog
    v-model="enumItemDialogVisible"
    :mode="enumItemDialogMode"
    :data="enumItemDialogData"
    @save="handleSaveEnumItem"
  />
  <ContentWrap>
    <div class="enum-manager">
      <div class="search">
        <div class="panel-header">
          <!-- 高级搜索表单 -->
          <AdvancedSearchForm
            v-model="formData"
            :form-config="EnumConfig.EnumFormConfig"
            :advanced-conditions="advancedConditions"
            :loading="enumItemLoading"
            @search="handleSearch"
            :showAdvancedSearch="false"
            @advanced-search="handleSearch"
          />
          <div class="flex">
            <ElButton type="primary" class="mr-0.5" @click="handleAddEnumItem">
              <Icon icon="ep:plus" />新增</ElButton
            >
            <ElButton type="primary" @click="downloadFunc" :loading="exportLoading">
              <Icon class="mr-0.5" icon="ep:upload-filled" />
              导出</ElButton
            >
          </div>
        </div>
      </div>
      <ElRow :gutter="0">
        <ElCol :span="5">
          <Left v-model:selected="selectedEnumType" :type="LeftType.enum" />
        </ElCol>
        <ElCol :span="19">
          <div class="enum-container">
            <!-- Right Panel - Enum Items -->
            <div class="enum-items-panel">
              <VxeGrid ref="enumItemTableRef" v-bind="tableOptions">
                <template #operation="{ row }">
                  <el-dropdown>
                    <el-button type="text"> 操作 </el-button>
                    <template #dropdown>
                      <el-dropdown-item>
                        <ElButton link @click="handleEditEnumItem(row)">
                          <Icon icon="ep:edit" />
                          编辑
                        </ElButton></el-dropdown-item
                      >
                      <el-dropdown-item>
                        <ElButton type="danger" link @click="handleDeleteEnumItem(row)">
                          <Icon icon="ep:delete" />
                          删除
                        </ElButton></el-dropdown-item
                      >
                    </template>
                  </el-dropdown>
                </template>
              </VxeGrid>
              <div class="flex justify-end mt-1 md-1">
                <ElPagination
                  ref="pagerRef"
                  v-model:current-page="enumItemPager.current"
                  v-model:page-size="enumItemPager.size"
                  :total="enumItemPager.total"
                  background
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="fetchEnumItems"
                  @current-change="fetchEnumItems"
                />
              </div>
            </div>
          </div>
        </ElCol>
      </ElRow>
    </div>
  </ContentWrap>
</template>

<style scoped lang="less">
.enum-manager {
  position: relative;
  width: 100%;
  height: 100%;
}

.enum-container {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 10px 10px 5px;
  border: 1px solid var(--el-border-color-light);
  gap: 16px;
}

.enum-items-panel {
  display: flex;
  height: 100%;
  overflow: hidden;
  border-radius: 0 4px 4px 0;
  flex: 1;
  flex-direction: column;
}

.panel-header {
  padding-bottom: 10px;
}

.title {
  font-size: 16px;
  font-weight: 500;
}

.search-box {
  padding: 12px;
}

.enum-types-list {
  padding: 8px;
  overflow-y: auto;
  flex: 1;
}

.search-actions {
  display: flex;
  gap: 8px;
  align-items: center;

  .el-input {
    width: 240px;
  }
}

:deep(.el-form-item) {
  .el-cascader,
  .el-select,
  .el-autocomplete {
    width: 100%;
  }

  .select-plus {
    display: block !important;
    width: 100%;
  }
}
</style>
