<script setup lang="ts">
import {
  CommandEnums,
  CommandPermission,
  SampleEnums
} from '@/views/basic-library-manage/sample-manage/const'
import { SampleListPageAPI } from '@/views/basic-library-manage/sample-manage/api/SampleList'
import {
  InitSampleInfoDialog,
  InitSampleReviewDialog
} from '@/views/basic-library-manage/sample-manage/components/InitSample'
import {
  ColorSampleInfoDialog,
  ColorSampleReviewDialog
} from '@/views/basic-library-manage/sample-manage/components/ColorSample'
import CommonCreateDialog from '@/views/basic-library-manage/sample-manage/components/CommonCreateDialog.vue'
import VersionDialog from '@/views/basic-library-manage/sample-manage/components/VersionDialog.vue'
import { useHelper } from '@/views/basic-library-manage/sample-manage/components/hooks'
import { DialogTypeEnums } from '@/enums'
import { Icon } from '@/components/Icon'
import { type VxeGridInstance, VxeGridProps } from 'vxe-table'
import { ref } from 'vue'
import { SampleInfoAPI } from '@/views/basic-library-manage/sample-manage/api/SampleInfo'
import { YesNoEnum } from '@/views/basic-library-manage/const'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { ElPagination } from 'element-plus'
defineOptions({ name: 'CommonSample' })

const dialogMap = {
  [SampleEnums.INIT]: {
    review: InitSampleReviewDialog,
    info: InitSampleInfoDialog,
    create: CommonCreateDialog,
    version: VersionDialog,
    addPer: 'addInitSample'
  },
  [SampleEnums.COLOR]: {
    review: ColorSampleReviewDialog,
    info: ColorSampleInfoDialog,
    create: CommonCreateDialog,
    version: VersionDialog,
    addPer: 'addColorSample'
  },
  [SampleEnums.CONFIRM]: {
    review: ColorSampleReviewDialog,
    info: ColorSampleInfoDialog,
    create: CommonCreateDialog,
    version: VersionDialog,
    addPer: 'addConfirmSample'
  }
}
const tableRef = ref<VxeGridInstance>()
const slots = useSlots()
const emits = defineEmits(['refresh', 'update:tableData'])
const props = defineProps<{
  type?: SampleEnums | undefined
  tableData: Ref<SampleListPageAPI.Row[]>
  tableOptions: VxeGridProps<SampleListPageAPI.Row> & {
    pager: Ref<InstanceType<typeof ElPagination>>
  }
  params?: SampleInfoAPI.ProofInfoResp
}>()
const enumType = ref<SampleEnums | undefined>(props.type)
const enumStatus = ref()
const tableDataContent = computed({
  get: () => props.tableData,
  set: (val) => emits('update:tableData', val)
})
const {
  currentRow,
  dialogType,
  dialogVisible,
  reviewDialogVisible,
  versionDialogVisible,
  handleVersion,
  handleCommand,
  handleCreateSample,
  handleCreate,
  createDialogVisible
} = useHelper(tableDataContent, tableRef)
const initDialog = (row: SampleListPageAPI.Row) => {
  currentRow.value = row
  dialogType.value = DialogTypeEnums.VIEW
  dialogVisible.value = true
  enumType.value = row.type || props.type
}
const handleCommandFn = async (command: CommandEnums, row: SampleListPageAPI.Row) => {
  enumType.value = row.type || props.type
  enumStatus.value = command
  const refreshCommandList = [
    CommandEnums.DELETE,
    CommandEnums.COPY,
    CommandEnums.SUBMIT,
    CommandEnums.REVOKE,
    CommandEnums.INVALID
  ]
  if (refreshCommandList.includes(command)) {
    await handleCommand(command, row)
    emits('refresh')
    return
  }
  await handleCommand(command, row)
}
//控制操作按钮是否显示
const isShow = computed(() => {
  return (row: SampleListPageAPI.Row) => {
    return [
      CommandEnums.DETAIL,
      CommandEnums.EDIT,
      CommandEnums.COPY,
      CommandEnums.DELETE,
      CommandEnums.SUBMIT,
      CommandEnums.REVIEW,
      CommandEnums.INVALID,
      CommandEnums.REVOKE
    ].some((action) => CommandPermission[action].includes(row.status!))
  }
})
//table高度
const maxHeight =
  props.tableOptions?.maxHeight ||
  useTableHeight({
    tableRef: tableRef,
    pagerRef: props.tableOptions?.pager
  })
defineExpose({
  tableRef
})
</script>

<template>
  <!---版本-->
  <component
    v-model="versionDialogVisible"
    :current-row="currentRow"
    v-if="enumType && dialogMap[enumType]"
    :type="enumType"
    :is="dialogMap[enumType]?.version"
  />
  <!---review-->
  <component
    v-model="reviewDialogVisible"
    :current-row="currentRow"
    :type="enumStatus"
    :isConfirm="enumType == SampleEnums.CONFIRM"
    v-if="enumType && dialogMap[enumType]"
    :is="dialogMap[enumType]?.review"
    @refresh="emits('refresh')"
  />
  <!---info-->
  <component
    v-model:modelValue="dialogVisible"
    :current-row="currentRow"
    :type="dialogType"
    :isConfirm="enumType == SampleEnums.CONFIRM"
    v-if="enumType && dialogMap[enumType]"
    :is="dialogMap[enumType]?.info"
    @edit="dialogType = DialogTypeEnums.EDIT"
    @refresh="emits('refresh')"
  />
  <!---create-->
  <component
    v-model="createDialogVisible"
    :current-row="params"
    :type="enumType"
    v-if="enumType && dialogMap[enumType]"
    :is="dialogMap[enumType]?.create"
    @submit="handleCreateSample"
  />
  <ElButton
    v-if="type"
    v-hasPermi="[dialogMap[type].addPer]"
    class="mb-2"
    type="primary"
    @click="
      () => {
        enumType = props.type
        handleCreate()
      }
    "
  >
    添加
  </ElButton>
  <template v-if="slots.button">
    <slot name="button"></slot>
  </template>
  <VxeGrid ref="tableRef" v-bind="tableOptions" :max-height="maxHeight - 75">
    <template #proofingSheet="{ row }">
      <OssUpload
        :model-value="row.proofingSheet ? [row.proofingSheet] : []"
        disabled
        list-type="text"
      />
    </template>
    <template #code="{ row }">
      <span class="p-0 max-w-full cursor-pointer text-blue-500" @click="initDialog(row)">
        {{ row.code }}
      </span>
    </template>
    <template #supplier="{ row }">
      <SelectPlus
        v-model="row.supplier"
        :data-method="(val) => val.map((e) => ({ ...e, disabled: e.useStatus !== +YesNoEnum.Y }))"
        :params="params && params.productId"
        api-key="getSampleSupplierList"
        disabled
        filterable
      />
    </template>
    <template #action="{ row }: { row: SampleListPageAPI.Row }">
      <ElDropdown @command="(val: CommandEnums) => handleCommandFn(val, row)" v-if="isShow(row)">
        <span class="el-dropdown-link">
          操作
          <Icon icon="ep:arrow-down" />
        </span>
        <template #dropdown>
          <ElDropdownMenu>
            <ElDropdownItem
              v-if="CommandPermission[CommandEnums.DETAIL].includes(row.status!)"
              :command="CommandEnums.DETAIL"
            >
              查看详情
            </ElDropdownItem>
            <ElDropdownItem
              v-if="CommandPermission[CommandEnums.EDIT].includes(row.status!)"
              :command="CommandEnums.EDIT"
            >
              编辑
            </ElDropdownItem>
            <ElDropdownItem
              v-if="CommandPermission[CommandEnums.SUBMIT].includes(row.status!)"
              :command="CommandEnums.SUBMIT"
            >
              提交打样
            </ElDropdownItem>
            <ElDropdownItem
              v-if="CommandPermission[CommandEnums.COPY].includes(row.status!)"
              :command="CommandEnums.COPY"
            >
              复制
            </ElDropdownItem>
            <ElDropdownItem
              v-if="CommandPermission[CommandEnums.DELETE].includes(row.status!)"
              :command="CommandEnums.DELETE"
            >
              删除
            </ElDropdownItem>
            <ElDropdownItem
              v-if="CommandPermission[CommandEnums.REVIEW].includes(row.status!)"
              :command="CommandEnums.REVIEW"
            >
              评审
            </ElDropdownItem>
            <ElDropdownItem
              v-if="CommandPermission[CommandEnums.INVALID].includes(row.status!)"
              :command="CommandEnums.INVALID"
            >
              作废
            </ElDropdownItem>
            <ElDropdownItem
              v-if="CommandPermission[CommandEnums.REVOKE].includes(row.status!)"
              :command="CommandEnums.REVOKE"
            >
              撤回
            </ElDropdownItem>
          </ElDropdownMenu>
        </template>
      </ElDropdown>
      <div
        ><ElButton
          link
          type="primary"
          @click="
            () => {
              enumType = row.type
              handleVersion(row)
            }
          "
          >版本记录</ElButton
        ></div
      >
    </template>
  </VxeGrid>
</template>

<style scoped lang="less"></style>
