<script setup lang="ts">
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { ProductSellingPointDetailAPI } from '@/api/productFeatures/types'
import { SelectPlus } from '@/components/Business/SelectPlus'
import {
  getProductSellPointDetail,
  getProductSellPointHistoryDetail,
  productSellingPointSave,
  productSellingPointUpdate
} from '@/api/productFeatures'
import { SellPointEnums } from '@/enums'
import { getFileNameFromUrl } from '@/utils'
import { StatusEnum } from '@/views/basic-library-manage/const'
import { ContentWrap } from '@/components/ContentWrap'
import { omit } from 'lodash-es'
defineOptions({
  name: 'FeatureInfo'
})
const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()
const useType = () => {
  const isEdit = computed(() => {
    return route.name === 'EditFeature'
  })
  const isView = computed(() => {
    return route.name === 'ViewFeature'
  })
  const isHistory = computed(() => {
    return route.name === 'HistoryFeature'
  })
  const isCreate = computed(() => {
    return route.name === 'CreateFeature'
  })
  console.log(route)
  return {
    isEdit,
    isCreate,
    isView,
    isHistory
  }
}
const { isEdit, isView, isHistory, isCreate } = useType()
const id = computed(() => {
  return route.query.id
})
const productNum = computed(() => {
  return route.query.productNumber
})

const formData = reactive<ProductSellingPointDetailAPI.QueryPdmSellPointDetailResp>({
  productNumber: undefined,
  sellPointProductTechnicalParameterDTO: {},
  pdmSellPointProductInfoDTO: {},
  sellPointBaseInfoDTO: {},
  sellPointComfortInfoDTO: {},
  sellPointOtherInfoDTO: {},
  sellPointSceneInfoDTO: {},
  sellPointFunctionDTO: {},
  sellPointTrendInfoDTO: {}
})
const rules = computed<FormRules<typeof formData>>(() => ({
  productNumber: [{ required: true, message: '请选择产品编号', trigger: 'change' }],
  'sellPointBaseInfoDTO.productStyle': [
    { required: true, message: '请选择产品风格', trigger: 'change' }
  ],
  'sellPointBaseInfoDTO.productSummary': [
    { required: true, message: '请输入产品简介', trigger: 'change' }
  ],
  'sellPointBaseInfoDTO.sellPointPriority': [
    { required: true, message: '请输入卖点优先级', trigger: 'change' }
  ],
  'sellPointSceneInfoDTO.sceneDressing': [
    { required: true, message: '请选择场景/穿搭', trigger: 'change' }
  ],
  'sellPointSceneInfoDTO.sceneDressingDesc': [
    {
      required: formData.sellPointSceneInfoDTO.sceneDressing !== SellPointEnums.SLASH,
      message: '请输入场景/穿搭说明',
      trigger: 'change'
    }
  ],
  'sellPointFunctionDTO.functionCharacteristics': [
    { required: true, message: '请选择功能/特性', trigger: 'change' }
  ],
  'sellPointFunctionDTO.functionCharacteristicsDesc': [
    {
      required: formData.sellPointFunctionDTO.functionCharacteristics !== SellPointEnums.SLASH,
      message: '请输入功能/特性说明',
      trigger: 'change'
    }
  ],
  'sellPointTrendInfoDTO.trendElement': [
    { required: true, message: '请输入趋势/元素', trigger: 'change' }
  ],
  'sellPointTrendInfoDTO.trendElementDesc': [
    {
      required: formData.sellPointTrendInfoDTO.trendElement !== SellPointEnums.SLASH,
      message: '请输入趋势/元素说明',
      trigger: 'change'
    }
  ],
  'sellPointComfortInfoDTO.comfortLevel': [
    { required: true, message: '请选择舒适度', trigger: 'change' }
  ],
  'sellPointComfortInfoDTO.comfortLevelDesc': [
    {
      required: formData.sellPointComfortInfoDTO.comfortLevel !== SellPointEnums.SLASH,
      message: '请输入舒适度说明',
      trigger: 'change'
    }
  ]
}))

const queryLoading = ref(false)
const getProductDetail = async () => {
  let featureData: ProductSellingPointDetailAPI.QueryPdmSellPointDetailResp = {}
  if (isHistory.value) {
    queryLoading.value = true
    const [error, result] = await getProductSellPointHistoryDetail(id.value)
    if (!error && result?.datas) {
      featureData = result.datas
    }
    queryLoading.value = false
  } else {
    if (!productNum.value && !formData.productNumber) {
      return false
    }
    queryLoading.value = true
    const [error, result] = await getProductSellPointDetail({
      id: id.value,
      productNumber: productNum.value || formData.productNumber
    })
    if (!error && result?.datas) {
      featureData = result.datas
    }
    queryLoading.value = false
  }

  queryLoading.value = false
  const {
    productNumber = '',
    pdmSellPointProductInfoDTO = {},
    sellPointBaseInfoDTO = {},
    sellPointComfortInfoDTO = {},
    sellPointOtherInfoDTO = {},
    sellPointSceneInfoDTO = {},
    sellPointTrendInfoDTO = {},
    sellPointFunctionDTO = {},
    sellPointProductTechnicalParameterDTO = {}
  } = featureData
  formData.productNumber = productNumber || productNum.value || formData.productNumber
  formData.sellPointProductTechnicalParameterDTO = sellPointProductTechnicalParameterDTO
  formData.pdmSellPointProductInfoDTO = pdmSellPointProductInfoDTO
  formData.sellPointBaseInfoDTO = sellPointBaseInfoDTO
  formData.sellPointComfortInfoDTO = sellPointComfortInfoDTO
  formData.sellPointOtherInfoDTO = sellPointOtherInfoDTO
  formData.sellPointSceneInfoDTO = sellPointSceneInfoDTO
  formData.sellPointTrendInfoDTO = sellPointTrendInfoDTO
  formData.sellPointFunctionDTO = sellPointFunctionDTO
}
enum DesignType {
  BASE = 'sellPointBaseInfoDTO',
  Scene = 'sellPointSceneInfoDTO',
  Function = 'sellPointFunctionDTO',
  Trend = 'sellPointTrendInfoDTO',
  Comfort = 'sellPointComfortInfoDTO',
  Other = 'sellPointOtherInfoDTO'
}

const designTypeList = [
  { label: '基本信息', value: DesignType.BASE },
  { label: '场景/穿搭', value: DesignType.Scene },
  { label: '功能/特性', value: DesignType.Function },
  { label: '趋势/元素', value: DesignType.Trend },
  { label: '舒适度', value: DesignType.Comfort },
  { label: '其他卖点', value: DesignType.Other }
]

const designType = ref<DesignType>(DesignType.BASE)

const activeNames = [1, 2, 3]

interface ProductDescriptionsItem {
  field:
    | keyof (ProductSellingPointDetailAPI.QueryPdmSellPointProductDTO & { productNumber?: number })
    | `${keyof ProductSellingPointDetailAPI.QueryPdmSellPointProductDTO}ItemName`
  label: string
  span?: number
}

const productDescriptionsItem: ProductDescriptionsItem[] = [
  { field: 'productNumber', label: '产品编号', span: 2 },
  { field: 'designUrl', label: '设计图' },
  { field: 'brandItemName', label: '品牌' },
  { field: 'productCategoryItemName', label: '产品类目' },
  { field: 'productPositioningItemName', label: '产品目标定级' },
  { field: 'launchSeasonItemName', label: '开发季节' },
  { field: 'styleWms', label: 'Style（WMS）' },
  { field: 'productActualPositionItemName', label: '产品实际定级' },
  { field: 'targetAudienceItemName', label: '适用人群' },
  { field: 'stylePositioningItemName', label: '款式定位' },
  { field: 'productType', label: '产品类型' },
  { field: 'applicableSeasonItemName', label: '适用季节' },
  { field: 'developmentTypeItemName', label: '开发类型' },
  { field: 'productLaunchSeason', label: '商品上市季节' },
  { field: 'lastsStandardItemName', label: '楦型标准' },
  { field: 'developmentStrategy', label: '开发策略' },
  { field: 'retailPrice', label: '吊牌价($)' },
  { field: 'toeStandardItemName', label: '楦头标准' },
  { field: 'developmentChannelItemName', label: '开发渠道' },
  { field: 'mainChannelMark', label: '主渠道标识' },
  { field: 'designPersonIdItemName', label: '产品企划' },
  { field: 'designerIdItemName', label: '产品设计师' },
  { field: 'chooseChannel', label: '选品渠道' },
  { field: 'combatTeam', label: 'CT归属' },
  { field: 'assignedFactoryItemName', label: '大货供应商' },
  { field: 'vocOriginalStyleNumber', label: '母Style' }
]

interface ProductTechnicalDescriptionsItem {
  field:
    | keyof ProductSellingPointDetailAPI.QuerySellPointProductTechnicalParameterDTO
    | `${keyof ProductSellingPointDetailAPI.QuerySellPointProductTechnicalParameterDTO}ItemName`
  label: string
  span?: number
}

const productTechnicalDescriptionsItem: ProductTechnicalDescriptionsItem[] = [
  { field: 'heelHeight', label: '跟高（时装鞋）' },
  { field: 'cylinderHeight', label: '筒高（靴类）' },
  { field: 'cylinderCircumference', label: '筒围（靴类）' },
  { field: 'testReport', label: '检测报告' }
]

const handleClose = () => {
  useClosePage('FeatureDetails', {
    state: {
      productNumber: productNum.value
    }
  })
}

const submitLoading = ref(false)
const handleSubmit = async () => {
  const valid = await formRef.value?.validate().catch((result) => {
    const keys = Object.keys(result)
    const designTypes = [
      DesignType.BASE,
      DesignType.Scene,
      DesignType.Function,
      DesignType.Trend,
      DesignType.Comfort
    ]

    const matchedType = designTypes.find((type) => keys.some((key) => key.includes(type)))

    if (matchedType) {
      designType.value = matchedType
      nextTick(() => formRef.value?.validate())
      return false
    }
  })
  if (!valid) return
  submitLoading.value = true
  const [error, result] = await productSellingPointUpdate({
    id: id.value,
    ...omit(formData, ['sellPointProductTechnicalParameterDTO'])
  })
  submitLoading.value = false
  if (!error && result?.msg) {
    ElMessage.success(result.msg)
    handleClose()
  }
}
const handleSave = async () => {
  formRef.value?.clearValidate()
  const valid = await formRef.value?.validateField('productNumber')
  if (!valid) return
  submitLoading.value = true
  const [error, result] = await productSellingPointSave({
    id: id.value,
    ...omit(formData, ['sellPointProductTechnicalParameterDTO'])
  })
  submitLoading.value = false
  if (!error && result?.msg) {
    ElMessage.success(result.msg)
    handleClose()
  }
  submitLoading.value = false
}
const handleEdit = () => {
  router.push({
    name: 'EditFeature',
    query: {
      id: id.value,
      productNumber: productNum.value
    }
  })
}
onActivated(() => {
  getProductDetail()
})
onMounted(getProductDetail)
</script>

<template>
  <ContentWrap class="info-wrapper">
    <ElForm
      ref="formRef"
      v-loading="queryLoading"
      :disabled="isHistory || isView"
      :model="formData"
      :rules="rules"
      :scroll-into-view-options="{ behavior: 'smooth' }"
      class="mx-4"
      label-width="auto"
      scroll-to-error
    >
      <ElCollapse v-model="activeNames">
        <ElCollapseItem :name="1" title="产品信息">
          <Descriptions
            :column="3"
            :data="formData.pdmSellPointProductInfoDTO"
            :schema="productDescriptionsItem"
          >
            <template #productNumber-label="rowContent"
              ><span class="is--required">{{ rowContent.label }}</span>
            </template>
            <template #productNumber>
              <ElFormItem prop="productNumber">
                <SelectPlus
                  v-model="formData.productNumber"
                  :configuration="{
                    key: 'id',
                    label: 'value',
                    value: 'value'
                  }"
                  :disabled="!!id"
                  api-key="getEffectProductIdList"
                  filterable
                  virtualized
                  @change="getProductDetail()"
                />
              </ElFormItem>
            </template>
            <template #designUrl>
              <div v-if="formData.pdmSellPointProductInfoDTO?.designUrl?.length">
                <ElImage
                  v-for="item in formData.pdmSellPointProductInfoDTO.designUrl"
                  :key="item.signatureUrl"
                  :preview-src-list="formData.pdmSellPointProductInfoDTO.designUrl.map(e => e.signatureUrl!)"
                  :src="item.signatureUrl"
                  hide-on-click-modal
                  class="!h-[80px]"
                  loading="lazy"
                />
              </div>
            </template>
          </Descriptions>
        </ElCollapseItem>
        <ElCollapseItem :name="2" title="产品技术参数信息">
          <Descriptions
            :data="formData.sellPointProductTechnicalParameterDTO"
            :schema="productTechnicalDescriptionsItem"
          >
            <template #testReport>
              <div v-if="formData.sellPointProductTechnicalParameterDTO?.testReport?.length">
                <ElLink
                  v-for="item in formData.sellPointProductTechnicalParameterDTO.testReport"
                  :key="item.signatureUrl"
                  :href="item.signatureUrl"
                  class="mr-2"
                  type="primary"
                >
                  <span v-if="item.signatureUrl">{{
                    item.fileName || getFileNameFromUrl(item.signatureUrl)
                  }}</span>
                </ElLink>
              </div>
            </template>
          </Descriptions>
        </ElCollapseItem>
        <ElCollapseItem :name="3" title="产品设计信息">
          <ElFormItem>
            <VxeRadioGroup v-model="designType">
              <VxeRadioButton
                v-for="item in designTypeList"
                :key="item.value"
                :content="item.label"
                :label="item.value"
              />
            </VxeRadioGroup>
          </ElFormItem>
          <ElRow v-show="designType === DesignType.BASE" :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="产品风格" prop="sellPointBaseInfoDTO.productStyle">
                <SelectPlus
                  v-model="formData.sellPointBaseInfoDTO.productStyle"
                  api-key="PRODUCT_STYLE"
                  cache
                  clearable
                  placeholder="请选择"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="6">
              <ElFormItem label="对标竞品" prop="sellPointBaseInfoDTO.benchmarking">
                <ElInput
                  v-model="formData.sellPointBaseInfoDTO.benchmarking"
                  maxlength="50"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="竞品款式图" prop="sellPointBaseInfoDTO.benchmarkingStyleImage">
                <OssUpload
                  v-model="formData.sellPointBaseInfoDTO.benchmarkingStyleImage"
                  :limit="5"
                  :size-limit="1024 * 1024 * 50"
                  accept="image/*"
                  drag
                  listType="picture-card"
                  multiple
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="14">
              <ElFormItem label="产品简介" prop="sellPointBaseInfoDTO.productSummary">
                <ElInput
                  v-model="formData.sellPointBaseInfoDTO.productSummary"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="1500"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="14">
              <ElFormItem label="卖点优先级" prop="sellPointBaseInfoDTO.sellPointPriority">
                <ElInput
                  v-model="formData.sellPointBaseInfoDTO.sellPointPriority"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="1500"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="系列名" prop="sellPointBaseInfoDTO.sellPointSeries">
                <ElInput
                  v-model="formData.sellPointBaseInfoDTO.sellPointSeries"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="50"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="特征F" prop="sellPointBaseInfoDTO.sellPointCharacteristic">
                <ElInput
                  v-model="formData.sellPointBaseInfoDTO.sellPointCharacteristic"
                  maxlength="500"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="优势A" prop="sellPointBaseInfoDTO.sellPointAdvantage">
                <ElInput
                  v-model="formData.sellPointBaseInfoDTO.sellPointAdvantage"
                  maxlength="500"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8" />
            <ElCol :span="8">
              <ElFormItem label="证据E" prop="sellPointBaseInfoDTO.sellPointEvidence">
                <ElInput
                  v-model="formData.sellPointBaseInfoDTO.sellPointEvidence"
                  maxlength="500"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="利益B" prop="sellPointBaseInfoDTO.sellPointInterest">
                <ElInput
                  v-model="formData.sellPointBaseInfoDTO.sellPointInterest"
                  maxlength="500"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="备注" prop="sellPointBaseInfoDTO.remark">
                <ElInput
                  v-model="formData.sellPointBaseInfoDTO.remark"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="3000"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow v-show="designType === DesignType.Function" :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="功能/特性" prop="sellPointFunctionDTO.functionCharacteristics">
                <SelectPlus
                  v-model="formData.sellPointFunctionDTO.functionCharacteristics"
                  api-key="FUNCTION_CHARACTERISTICS"
                  cache
                  clearable
                  placeholder="请选择"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="功能/特性说明"
                prop="sellPointFunctionDTO.functionCharacteristicsDesc"
              >
                <ElInput
                  v-model="formData.sellPointFunctionDTO.functionCharacteristicsDesc"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="500"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="功能/特性 (A+呈现参考）"
                prop="sellPointFunctionDTO.functionCharacteristicsAlphaPresentReference"
              >
                <ElInput
                  v-model="
                    formData.sellPointFunctionDTO.functionCharacteristicsAlphaPresentReference
                  "
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="3000"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="功能/特性 (图片呈现参考）"
                prop="sellPointFunctionDTO.sellPointFunctionImage"
              >
                <OssUpload
                  v-model="formData.sellPointFunctionDTO.sellPointFunctionImage"
                  :limit="5"
                  :size-limit="1024 * 1024 * 50"
                  accept="image/*"
                  drag
                  listType="picture-card"
                  multiple
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="功能/特性 (视频呈现参考）"
                prop="sellPointFunctionDTO.sellPointFunctionVideo"
              >
                <OssUpload
                  v-model="formData.sellPointFunctionDTO.sellPointFunctionVideo"
                  :limit="1"
                  :size-limit="1024 * 1024 * 500"
                  accept="video/*"
                  class="w-full"
                  drag
                  listType="text"
                >
                  <template #trigger>
                    <ElButton>上传视频</ElButton>
                  </template>
                  <template #tip> 支持所有视频格式，单个文件不能超过500MB </template>
                </OssUpload>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="功能/特性 (文件呈现参考）"
                prop="sellPointFunctionDTO.sellPointFunctionFile"
              >
                <OssUpload
                  v-model="formData.sellPointFunctionDTO.sellPointFunctionFile"
                  :limit="1"
                  :size-limit="1024 * 1024 * 100"
                  class="w-full"
                  drag
                  listType="text"
                >
                  <template #tip> 支持所有文件格式，单个文件不能超过100MB </template>
                </OssUpload>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="功能/特性 (站外呈现参考) "
                prop="sellPointFunctionDTO.functionCharacteristicsExternalPresentReference"
              >
                <ElInput
                  v-model="
                    formData.sellPointFunctionDTO.functionCharacteristicsExternalPresentReference
                  "
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="3000"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow v-show="designType === DesignType.Scene" :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="场景/穿搭" prop="sellPointSceneInfoDTO.sceneDressing">
                <SelectPlus
                  v-model="formData.sellPointSceneInfoDTO.sceneDressing"
                  api-key="SCENE_DRESSING"
                  cache
                  clearable
                  placeholder="请选择"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="场景/穿搭说明" prop="sellPointSceneInfoDTO.sceneDressingDesc">
                <ElInput
                  v-model="formData.sellPointSceneInfoDTO.sceneDressingDesc"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="500"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="场景/穿搭 (A+呈现参考）"
                prop="sellPointSceneInfoDTO.sceneAlphaPresentReference"
              >
                <ElInput
                  v-model="formData.sellPointSceneInfoDTO.sceneAlphaPresentReference"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="3000"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="场景/穿搭 (图片呈现参考）"
                prop="sellPointSceneInfoDTO.sceneFunctionImage"
              >
                <OssUpload
                  v-model="formData.sellPointSceneInfoDTO.sceneFunctionImage"
                  :limit="5"
                  :size-limit="1024 * 1024 * 50"
                  accept="image/*"
                  drag
                  listType="picture-card"
                  multiple
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="场景/穿搭 (视频呈现参考）"
                prop="sellPointSceneInfoDTO.sceneFunctionVideo"
              >
                <OssUpload
                  v-model="formData.sellPointSceneInfoDTO.sceneFunctionVideo"
                  :limit="1"
                  :size-limit="1024 * 1024 * 500"
                  accept="video/*"
                  class="w-full"
                  drag
                  listType="text"
                >
                  <template #trigger>
                    <ElButton>上传视频</ElButton>
                  </template>
                  <template #tip> 支持所有视频格式，单个文件不能超过500MB </template>
                </OssUpload>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="场景/穿搭 (文件呈现参考）"
                prop="sellPointSceneInfoDTO.sceneFunctionFile"
              >
                <OssUpload
                  v-model="formData.sellPointSceneInfoDTO.sceneFunctionFile"
                  :limit="1"
                  :size-limit="1024 * 1024 * 100"
                  class="w-full"
                  drag
                  listType="text"
                >
                  <template #tip> 支持所有文件格式，单个文件不能超过100MB </template>
                </OssUpload>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="场景/穿搭 (站外呈现参考) "
                prop="sellPointSceneInfoDTO.sceneExternalPresentReference"
              >
                <ElInput
                  v-model="formData.sellPointSceneInfoDTO.sceneExternalPresentReference"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="3000"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow v-show="designType === DesignType.Trend" :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="趋势/元素" prop="sellPointTrendInfoDTO.trendElement">
                <SelectPlus
                  v-model="formData.sellPointTrendInfoDTO.trendElement"
                  api-key="TREND_ELEMENT"
                  cache
                  clearable
                  placeholder="请选择"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="趋势/元素说明" prop="sellPointTrendInfoDTO.trendElementDesc">
                <ElInput
                  v-model="formData.sellPointTrendInfoDTO.trendElementDesc"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="500"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="趋势/元素 (A+呈现参考）"
                prop="sellPointTrendInfoDTO.trendAlphaPresentReference"
              >
                <ElInput
                  v-model="formData.sellPointTrendInfoDTO.trendAlphaPresentReference"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="3000"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="趋势/元素 (图片呈现参考）"
                prop="sellPointTrendInfoDTO.trendElementImage"
              >
                <OssUpload
                  v-model="formData.sellPointTrendInfoDTO.trendElementImage"
                  :limit="5"
                  :size-limit="1024 * 1024 * 50"
                  accept="image/*"
                  drag
                  listType="picture-card"
                  multiple
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="趋势/元素 (视频呈现参考）"
                prop="sellPointTrendInfoDTO.trendElementVideo"
              >
                <OssUpload
                  v-model="formData.sellPointTrendInfoDTO.trendElementVideo"
                  :limit="1"
                  :size-limit="1024 * 1024 * 500"
                  accept="video/*"
                  class="w-full"
                  drag
                  listType="text"
                >
                  <template #trigger>
                    <ElButton>上传视频</ElButton>
                  </template>
                  <template #tip> 支持所有视频格式，单个文件不能超过500MB </template>
                </OssUpload>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="趋势/元素 (文件呈现参考）"
                prop="sellPointTrendInfoDTO.trendElementFile"
              >
                <OssUpload
                  v-model="formData.sellPointTrendInfoDTO.trendElementFile"
                  :limit="1"
                  :size-limit="1024 * 1024 * 100"
                  class="w-full"
                  drag
                  listType="text"
                >
                  <template #tip> 支持所有文件格式，单个文件不能超过100MB </template>
                </OssUpload>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="趋势/元素 (站外呈现参考) "
                prop="sellPointTrendInfoDTO.trendExternalPresentReference"
              >
                <ElInput
                  v-model="formData.sellPointTrendInfoDTO.trendExternalPresentReference"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="3000"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow v-show="designType === DesignType.Comfort" :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="舒适度" prop="sellPointComfortInfoDTO.comfortLevel">
                <SelectPlus
                  v-model="formData.sellPointComfortInfoDTO.comfortLevel"
                  api-key="COMFORT_LEVEL"
                  cache
                  clearable
                  placeholder="请选择"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="舒适度说明" prop="sellPointComfortInfoDTO.comfortLevelDesc">
                <ElInput
                  v-model="formData.sellPointComfortInfoDTO.comfortLevelDesc"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="500"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="舒适度 (A+呈现参考）"
                prop="sellPointComfortInfoDTO.comfortAlphaPresentReference"
              >
                <ElInput
                  v-model="formData.sellPointComfortInfoDTO.comfortAlphaPresentReference"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="3000"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="舒适度 (图片呈现参考）"
                prop="sellPointComfortInfoDTO.comfortLevelImage"
              >
                <OssUpload
                  v-model="formData.sellPointComfortInfoDTO.comfortLevelImage"
                  :limit="5"
                  :size-limit="1024 * 1024 * 50"
                  accept="image/*"
                  drag
                  listType="picture-card"
                  multiple
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="舒适度 (视频呈现参考）"
                prop="sellPointComfortInfoDTO.comfortLevelVideo"
              >
                <OssUpload
                  v-model="formData.sellPointComfortInfoDTO.comfortLevelVideo"
                  :limit="1"
                  :size-limit="1024 * 1024 * 500"
                  accept="video/*"
                  class="w-full"
                  drag
                  listType="text"
                >
                  <template #trigger>
                    <ElButton>上传视频</ElButton>
                  </template>
                  <template #tip> 支持所有视频格式，单个文件不能超过500MB </template>
                </OssUpload>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="舒适度 (文件呈现参考）"
                prop="sellPointComfortInfoDTO.comfortLevelFile"
              >
                <OssUpload
                  v-model="formData.sellPointComfortInfoDTO.comfortLevelFile"
                  :limit="1"
                  :size-limit="1024 * 1024 * 100"
                  class="w-full"
                  drag
                  listType="text"
                >
                  <template #tip> 支持所有文件格式，单个文件不能超过100MB </template>
                </OssUpload>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="舒适度 (站外呈现参考) "
                prop="sellPointComfortInfoDTO.comfortExternalPresentReference"
              >
                <ElInput
                  v-model="formData.sellPointComfortInfoDTO.comfortExternalPresentReference"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="3000"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow v-show="designType === DesignType.Other" :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="其他卖点" prop="sellPointOtherInfoDTO.sellPointOther">
                <ElInput
                  v-model="formData.sellPointOtherInfoDTO.sellPointOther"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="500"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="其他卖点说明" prop="sellPointSceneInfoDTO.sellPointOtherDesc">
                <ElInput
                  v-model="formData.sellPointOtherInfoDTO.sellPointOtherDesc"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="500"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="其他卖点 (A+呈现参考）"
                prop="sellPointOtherInfoDTO.comfortAlphaPresentReference"
              >
                <ElInput
                  v-model="formData.sellPointOtherInfoDTO.otherAlphaPresentReference"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="3000"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="其他卖点 (图片呈现参考）"
                prop="sellPointOtherInfoDTO.sellPointOtherImage"
              >
                <OssUpload
                  v-model="formData.sellPointOtherInfoDTO.sellPointOtherImage"
                  :limit="5"
                  :size-limit="1024 * 1024 * 50"
                  accept="image/*"
                  drag
                  listType="picture-card"
                  multiple
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="其他卖点 (视频呈现参考）"
                prop="sellPointOtherInfoDTO.sellPointOtherVideo"
              >
                <OssUpload
                  v-model="formData.sellPointOtherInfoDTO.sellPointOtherVideo"
                  :limit="1"
                  :size-limit="1024 * 1024 * 500"
                  accept="video/*"
                  class="w-full"
                  drag
                  listType="text"
                >
                  <template #trigger>
                    <ElButton>上传视频</ElButton>
                  </template>
                  <template #tip> 支持所有视频格式，单个文件不能超过500MB </template>
                </OssUpload>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="其他卖点 (文件呈现参考）"
                prop="sellPointOtherInfoDTO.sellPointOtherFile"
              >
                <OssUpload
                  v-model="formData.sellPointOtherInfoDTO.sellPointOtherFile"
                  :limit="1"
                  :size-limit="1024 * 1024 * 100"
                  class="w-full"
                  drag
                  listType="text"
                >
                  <template #tip> 支持所有文件格式，单个文件不能超过100MB </template>
                </OssUpload>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="其他卖点 (站外呈现参考) "
                prop="sellPointOtherInfoDTO.otherExternalPresentReference"
              >
                <ElInput
                  v-model="formData.sellPointOtherInfoDTO.otherExternalPresentReference"
                  :autosize="{
                    minRows: 3
                  }"
                  maxlength="3000"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCollapseItem>
      </ElCollapse>
    </ElForm>
  </ContentWrap>
  <ContentWrap class="mt-2">
    <div class="text-center">
      <ElButton :loading="submitLoading" @click="handleClose">返回</ElButton>
      <ElButton
        :loading="submitLoading"
        @click="handleSave"
        v-if="
          !isView && (formData?.sellPointBaseInfoDTO.sellPointStatus == StatusEnum.DRAFT || !id)
        "
        >暂存</ElButton
      >
      <ElButton
        v-if="isEdit || isCreate"
        :loading="submitLoading"
        type="primary"
        @click="handleSubmit"
        >确定</ElButton
      >
      <ElButton v-if="isView" :loading="submitLoading" type="primary" @click="handleEdit">
        修改产品卖点
      </ElButton>
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped>
.is--required {
  &:before {
    margin-right: 4px;
    color: var(--el-color-danger);
    content: '*';
  }
}
</style>
