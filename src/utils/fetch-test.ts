// Fetch 拦截器测试文件
import { get, postJSON } from './fetch'
import { setToken } from './token'

// 测试函数
export const testFetchInterceptors = async () => {
  console.log('=== 测试 Fetch 拦截器 ===')
  
  // 设置测试 token
  setToken('test_access_token_123')
  
  try {
    // 测试 GET 请求
    console.log('1. 测试 GET 请求...')
    const getResult = await get({
      url: '/test/get',
      params: {
        testParam: 'test_value'
      }
    })
    console.log('GET 请求结果:', getResult)
    
    // 测试 POST 请求
    console.log('2. 测试 POST 请求...')
    const postResult = await postJSON({
      url: '/test/post',
      data: {
        testData: 'test_value',
        userId: '12345'
      }
    })
    console.log('POST 请求结果:', postResult)
    
    // 测试没有 token 的 POST 请求
    console.log('3. 测试没有 token 的 POST 请求...')
    // 临时清除 token
    localStorage.removeItem('access_token')
    
    const postWithoutTokenResult = await postJSON({
      url: '/test/post-no-token',
      data: {
        testData: 'test_value_no_token'
      }
    })
    console.log('没有 token 的 POST 请求结果:', postWithoutTokenResult)
    
  } catch (error) {
    console.error('测试过程中出现错误:', error)
  }
}

// 在开发环境下可以调用此函数进行测试
if (import.meta.env.DEV) {
  // testFetchInterceptors()
}
