import type { AxiosRequestConfig, AxiosResponse, Method } from 'axios'
import axios, { AxiosError } from 'axios'
// todo 适配web与h5
import { ElMessage } from 'element-plus'
import { showToast } from 'vant'
import to from 'await-to-js'
import { getToken, clearAuth } from './token'

const fetch = axios.create({
  adapter: 'fetch',
  timeout: 10000,
  baseURL: import.meta.env.DEV ? '' : import.meta.env.VITE_API_URL,
})

function getErrorMsg() {
  return {
    CLIENT_ERROR: '客户端出错，请稍后再试!',
    SERVER_ERROR: '服务器出错，请稍后再试!',
    DEFAULT_ERROR: '未知错误，请稍后再试!',
  }
}

enum ResponseCode {
  REDIRECTION = '302',
  UNAUTHORIZED = '401',
}

function handleError(msg: string = getErrorMsg().DEFAULT_ERROR) {
  // 检测当前环境，使用对应的消息提示
  if (typeof window !== 'undefined') {
    // 检查是否为移动端环境
    const isMobile = window.location.pathname.includes('/mobile') ||
                     window.location.port === '5174'

    if (isMobile) {
      showToast(msg)
    } else {
      ElMessage.error(msg)
    }
  }
}
fetch.interceptors.request.use(
  (config) => {
    if (config.headers) {
      // 添加 Token 到请求头
      const token = getToken()
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
        config.headers['access-token'] = token
      }

      // 添加其他通用头部
      config.headers['x-referer'] = location.href
    }
    return config
  },
  (error) => {
    handleError(error.message || getErrorMsg().CLIENT_ERROR)
    return Promise.reject(error)
  },
)
fetch.interceptors.response.use(
  (response: AxiosResponse<any>) => {
    const { data, config } = response
    if (config.fetchOptions?.rawData) {
      return response
    }
    if (
      'responseCode' in data &&
      (ResponseCode.REDIRECTION === data.responseCode ||
        ResponseCode.UNAUTHORIZED === data.responseCode)
    ) {
      // Token 过期或未授权，清除认证信息并跳转到登录页
      clearAuth()
      if (data.data?.redirectPath) {
        location.replace(data.data.redirectPath)
      } else {
        // 根据当前环境跳转到对应的登录页
        const isMobile = window.location.pathname.includes('/mobile') ||
                         window.location.port === '5174'
        location.replace(isMobile ? '/mobile/#/login' : '/#/login')
      }
      return response
    }
    if ('resultCode' in data) {
      if (data.resultCode !== '00000000') {
        handleError(data.resultDesc || getErrorMsg().SERVER_ERROR)
        return Promise.reject(response)
      }
      return response
    }
    return response
  },
  (error) => {
    if (error.code === AxiosError.ERR_CANCELED || error.code === AxiosError.ECONNABORTED) {
      return
    }
    handleError(error.message || getErrorMsg().SERVER_ERROR)
    return Promise.reject(error)
  },
)

async function formatRequest<T>(
  method: Method,
  config: AxiosRequestConfig,
  headers: AxiosRequestConfig['headers'] = {},
) {
  const formatConfig = {
    method,
    ...config,
    headers: {
      ...headers,
      ...config.headers,
    },
  }
  const [error, response] = await to<AxiosResponse<T>>(fetch.request(formatConfig))

  if (!error && response) {
    return response.data
  }
}

// GET 请求方法
export function get<T>(config: AxiosRequestConfig) {
  return formatRequest<T>('GET', config)
}

// POST JSON 请求方法
export function postJSON<T>(config: AxiosRequestConfig) {
  return formatRequest<T>('POST', config, {
    ...config.headers,
    'Content-Type': 'application/json',
  })
}

export function postFormData<T>(config: AxiosRequestConfig) {
  return formatRequest<T>('POST', config, {
    ...config.headers,
    'Content-Type': 'multipart/form-data',
  })
}

export { fetch }
