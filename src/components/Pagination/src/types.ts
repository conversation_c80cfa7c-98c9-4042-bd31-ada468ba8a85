export interface Props {
  /** 当前页码 */
  currentPage: number
  /** 每页条数 */
  perPageCount: number
  /** 总条数 */
  totalCount: number
  /** 总页数 */
  totalPageCount?: number
  /** 是否显示页码信息 */
  showInfo?: boolean
  /** 页码信息的翻译键前缀，如 'orders.pagination' */
  translationPrefix?: string
}

export interface Emits {
  (e: 'page-change', page: number): void
  (e: 'first-page'): void
  (e: 'prev-page'): void
  (e: 'next-page'): void
  (e: 'last-page'): void
}
