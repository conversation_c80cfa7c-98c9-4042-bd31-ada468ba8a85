<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Icon } from '@/components/Icon'
import type { Props, Emits } from './types'

const props = withDefaults(defineProps<Props>(), {
  showInfo: true,
  translationPrefix: 'common.pagination',
})

const emit = defineEmits<Emits>()
const { t } = useI18n()

// 计算总页数
const computedTotalPageCount = computed(() => {
  return props.totalPageCount || Math.ceil(props.totalCount / props.perPageCount)
})

// 计算当前显示的条目范围
const currentRangeStart = computed(() => {
  return (props.currentPage - 1) * props.perPageCount + 1
})

const currentRangeEnd = computed(() => {
  return Math.min(props.currentPage * props.perPageCount, props.totalCount)
})

// 分页信息文本
const paginationInfo = computed(() => {
  const ofText = t(`${props.translationPrefix}.of`)
  return `${currentRangeStart.value}-${currentRangeEnd.value} ${ofText} ${props.totalCount}`
})

// 按钮禁用状态
const isFirstPage = computed(() => props.currentPage === 1)
const isLastPage = computed(() => props.currentPage === computedTotalPageCount.value)

// 事件处理
const handlePageChange = (page: number) => {
  if (page >= 1 && page <= computedTotalPageCount.value && page !== props.currentPage) {
    emit('page-change', page)
  }
}

const handleFirstPage = () => {
  if (!isFirstPage.value) {
    handlePageChange(1)
    emit('first-page')
  }
}

const handlePrevPage = () => {
  if (!isFirstPage.value) {
    handlePageChange(props.currentPage - 1)
    emit('prev-page')
  }
}

const handleNextPage = () => {
  if (!isLastPage.value) {
    handlePageChange(props.currentPage + 1)
    emit('next-page')
  }
}

const handleLastPage = () => {
  if (!isLastPage.value) {
    handlePageChange(computedTotalPageCount.value)
    emit('last-page')
  }
}
</script>

<template>
  <div class="pagination">
    <span v-if="showInfo" class="pagination-info">{{ paginationInfo }}</span>

    <div class="pagination-controls">
      <!-- 首页按钮 -->
      <span
        class="pagination-button"
        :class="{ disabled: isFirstPage }"
        @click="handleFirstPage"
        :title="t('common.pagination.firstPage')"
      >
        <Icon icon="svg-icon:first-arrow" size="24"></Icon>
      </span>

      <!-- 上一页按钮 -->
      <span
        class="pagination-button"
        :class="{ disabled: isFirstPage }"
        @click="handlePrevPage"
        :title="t('common.pagination.prevPage')"
      >
        <Icon icon="svg-icon:left-arrow" size="24"></Icon>
      </span>

      <!-- 下一页按钮 -->
      <span
        class="pagination-button"
        :class="{ disabled: isLastPage }"
        @click="handleNextPage"
        :title="t('common.pagination.nextPage')"
      >
        <Icon icon="svg-icon:right-arrow" size="24"></Icon>
      </span>

      <!-- 末页按钮 -->
      <span
        class="pagination-button"
        :class="{ disabled: isLastPage }"
        @click="handleLastPage"
        :title="t('common.pagination.lastPage')"
      >
        <Icon icon="svg-icon:last-arrow" size="24"></Icon>
      </span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.pagination {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 24px 0;
  font-size: 20px;
}

.pagination-info {
  color: #000000;
  margin-right: 68px;
}

.pagination-controls {
  display: flex;
  gap: 40px;
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}
</style>
