import type { FormInstance } from 'element-plus'

// 表单项配置接口
export interface FormFieldConfig {
  label: string
  field: string
  component: any
  span?: number
  props?: Record<string, any>
  options?: Array<{ label: string; value: any; key?: string }>
  isAdvanced?: boolean // 是否为高级搜索项
}

// 高级搜索条件接口
export interface AdvancedCondition {
  field: string
  operator: string
  value: any
}

// 组件实例接口
export interface AdvancedSearchFormInstance {
  formRef: FormInstance | undefined
  validate: () => Promise<boolean>
  resetFields: () => void
}
