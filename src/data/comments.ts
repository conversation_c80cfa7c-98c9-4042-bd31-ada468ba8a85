import { getImageUrl } from '@/utils/getImageUrl'

// 生成星级评分
export const generateStars = (rating: number) => {
  return Array.from({ length: 5 }, (_, i) => i < rating)
}

// 用户评论数据
export const comments = [
  {
    id: 1,
    avatar: getImageUrl('comment.png'),
    name: '<PERSON><PERSON> from App Store',
    date: '2025年04月21日',
    rating: 5,
    title: 'Amazing app!',
    content:
      'I usually travel to hidden places and I use this app to help me stay in touch with my family. I love how convenient their service is… The signal is amazing and the customer service is GREAT! Max helped me troubleshoot my device when I was unable to connect and everything worked perfectly.',
  },
  {
    id: 2,
    avatar: getImageUrl('comment.png'),
    name: '<PERSON> from App Store',
    date: '2025年04月21日',
    rating: 5,
    title: 'Great deadzone',
    content:
      'No need to get up and buy a sim card for the place your traveling to, just buy in app and enjoy the freedom of that worry. Its really easy and wonderful in my experience, and price is as good as can be plus they offer really cool coupons which come in handy!!!',
  },
  {
    id: 3,
    avatar: getImageUrl('comment.png'),
    name: 'Boom<PERSON> 5877 from App Store',
    date: '2025年04月23日',
    rating: 5,
    title: 'Great help',
    content:
      'I admit to knowing very little about data usage but when we lost internet service due to a storm I needed help <PERSON> did a great job of explaining and setting me on the right path I appreciate the 24/7 support',
  },
  {
    id: 4,
    avatar: getImageUrl('comment.png'),
    name: 'Carolyn Jones from Play Store',
    date: '2025年04月25日',
    rating: 5,
    title: '',
    content:
      'No problems since I bought it, service is fast, and the connection is strong. The prices are reasonably good too.',
  },
  {
    id: 5,
    avatar: getImageUrl('comment.png'),
    name: 'Emmy Biga from Play Store',
    date: '2025年04月25日',
    rating: 5,
    title: '',
    content:
      "I've been using the service for years and it has consistently worked great.The customer support is very helpful and respond very fast.",
  },
]

// 平台评分数据
export const platforms = [
  {
    name: 'App Store',
    logo: 'appstore',
    rating: 4.7,
  },
  {
    name: 'Google Play',
    logo: 'playstore',
    rating: 4.7,
  },
  {
    name: 'Trustpilot',
    logo: 'trustpilot',
    rating: 4.7,
  },
]
