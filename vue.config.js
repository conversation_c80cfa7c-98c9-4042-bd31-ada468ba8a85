const { defineConfig } = require('@vue/cli-service')
const { sentryWebpackPlugin } = require('@sentry/webpack-plugin')

module.exports = defineConfig({
  transpileDependencies: true,
  productionSourceMap: true,
  configureWebpack: {
    devtool: 'source-map',
    plugins: [
      sentryWebpackPlugin({
        org: "your-org",
        project: "test-web",
        authToken: process.env.SENTRY_AUTH_TOKEN,
        url: "http://localhost:9000", // 您的本地Sentry地址
        include: './dist',
        ignore: ['node_modules', 'vue.config.js'],
      }),
    ],
  }
})